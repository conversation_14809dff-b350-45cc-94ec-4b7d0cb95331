#!/usr/bin/env python3
"""
数据验证脚本
验证股票数据的准确性，特别是价格数据
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def validate_stock_data(stock_code, data_dir="data/H_daily"):
    """验证单只股票的数据"""
    file_path = os.path.join(data_dir, f"{stock_code}.csv")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        df = pd.read_csv(file_path)
        df['time_key'] = pd.to_datetime(df['time_key'])
        df = df.sort_values('time_key').reset_index(drop=True)
        
        print(f"\n📊 {stock_code} ({df['name'].iloc[0]}) 数据验证")
        print("=" * 60)
        
        # 基本信息
        print(f"数据记录数: {len(df)}")
        print(f"数据时间范围: {df['time_key'].min()} 到 {df['time_key'].max()}")
        
        # 最新数据
        latest = df.iloc[-1]
        print(f"\n📅 最新数据 ({latest['time_key'].strftime('%Y-%m-%d')}):")
        print(f"  最新价格: {latest['close']:.2f} 港币")
        print(f"  开盘价: {latest['open']:.2f}")
        print(f"  最高价: {latest['high']:.2f}")
        print(f"  最低价: {latest['low']:.2f}")
        print(f"  成交量: {latest['volume']:,}")
        print(f"  PE比率: {latest.get('pe_ratio', 'N/A')}")
        
        # 价格合理性检查
        print(f"\n🔍 价格合理性检查:")
        
        # 检查价格是否在合理范围内
        recent_data = df.tail(30)  # 最近30天
        price_mean = recent_data['close'].mean()
        price_std = recent_data['close'].std()
        
        print(f"  最近30天平均价格: {price_mean:.2f}")
        print(f"  最近30天价格标准差: {price_std:.2f}")
        
        # 检查异常价格变动
        df['price_change'] = df['close'].pct_change() * 100
        extreme_changes = df[abs(df['price_change']) > 20]  # 单日涨跌超过20%
        
        if len(extreme_changes) > 0:
            print(f"  ⚠️  发现 {len(extreme_changes)} 个极端价格变动日 (单日涨跌>20%):")
            for _, row in extreme_changes.tail(5).iterrows():
                print(f"    {row['time_key'].strftime('%Y-%m-%d')}: {row['price_change']:+.1f}% (价格: {row['close']:.2f})")
        else:
            print(f"  ✅ 未发现异常的极端价格变动")
        
        # 检查价格连续性
        price_gaps = df['close'].diff()
        large_gaps = df[abs(price_gaps) > price_mean * 0.1]  # 价格跳空超过平均价格的10%
        
        if len(large_gaps) > 0:
            print(f"  ⚠️  发现 {len(large_gaps)} 个较大价格跳空:")
            for _, row in large_gaps.tail(3).iterrows():
                gap = price_gaps[row.name]
                print(f"    {row['time_key'].strftime('%Y-%m-%d')}: 跳空 {gap:+.2f} (价格: {row['close']:.2f})")
        else:
            print(f"  ✅ 价格连续性良好")
        
        # 港股通数据检查
        hsgt_data = df[df['hsgt_holding_ratio'].notna()]
        if len(hsgt_data) > 0:
            print(f"\n🌏 港股通数据:")
            latest_hsgt = hsgt_data.iloc[-1]
            print(f"  最新持股比例: {latest_hsgt['hsgt_holding_ratio']:.2f}%")
            print(f"  最新持股市值: {latest_hsgt['hsgt_holding_value']/10000:.0f} 万港币")
            print(f"  港股通数据记录数: {len(hsgt_data)}")
        else:
            print(f"\n🌏 港股通数据: 无数据")
        
        # 数据质量评分
        score = 100
        if len(extreme_changes) > 10:
            score -= 20
        if len(large_gaps) > 5:
            score -= 15
        if latest['close'] <= 0:
            score -= 50
        if pd.isna(latest['pe_ratio']):
            score -= 5
        
        print(f"\n📈 数据质量评分: {score}/100")
        
        if score >= 90:
            print("✅ 数据质量优秀")
        elif score >= 70:
            print("⚠️  数据质量良好，但有一些异常")
        else:
            print("❌ 数据质量较差，需要检查")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取数据时出错: {e}")
        return False

def validate_portfolio_data(portfolio_file="portfolio_monitor/portfolio_list_1"):
    """验证投资组合中所有股票的数据"""
    print("🔍 投资组合数据验证")
    print("=" * 80)
    
    try:
        portfolio_df = pd.read_csv(portfolio_file, sep=r'\s+', engine='python')
        portfolio_df['code_clean'] = portfolio_df['code'].str.replace('HK.', '', regex=False)
        
        print(f"投资组合包含 {len(portfolio_df)} 只股票")
        
        for _, row in portfolio_df.iterrows():
            stock_code = row['code_clean']
            validate_stock_data(stock_code)
            print()
        
    except Exception as e:
        print(f"❌ 读取投资组合文件失败: {e}")

def check_price_reasonableness():
    """检查价格合理性的专门函数"""
    print("💰 价格合理性专项检查")
    print("=" * 80)
    
    # 长城汽车价格验证
    print("🚗 长城汽车 (02333) 价格验证:")
    
    # 从多个来源验证价格
    file_path = "data/H_daily/02333.csv"
    if os.path.exists(file_path):
        df = pd.read_csv(file_path)
        df['time_key'] = pd.to_datetime(df['time_key'])
        latest = df.iloc[-1]
        
        print(f"  数据文件最新价格: {latest['close']:.2f} 港币")
        print(f"  数据日期: {latest['time_key'].strftime('%Y-%m-%d')}")
        
        # 检查价格是否在合理范围内（长城汽车历史价格范围）
        # 根据历史数据，长城汽车价格通常在10-50港币之间
        if 10 <= latest['close'] <= 500:
            print("  ✅ 价格在合理范围内")
        else:
            print(f"  ⚠️  价格可能异常，请核实")
        
        # 显示最近几天的价格走势
        recent = df.tail(5)
        print(f"\n  最近5天价格走势:")
        for _, row in recent.iterrows():
            change = row.get('change_rate', 0)
            print(f"    {row['time_key'].strftime('%Y-%m-%d')}: {row['close']:.2f} ({change:+.2f}%)")
    
    else:
        print("  ❌ 未找到长城汽车数据文件")

def main():
    """主函数"""
    print("🔍 股票数据验证工具")
    print("=" * 80)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 价格合理性检查
    check_price_reasonableness()
    print()
    
    # 2. 验证投资组合数据
    validate_portfolio_data()
    
    print("✅ 数据验证完成")

if __name__ == "__main__":
    main()
