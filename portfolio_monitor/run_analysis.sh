#!/bin/bash

# 投资组合分析报告生成脚本
# 使用方法: ./run_analysis.sh [portfolio_number] [real_time|historical]
# 例如: ./run_analysis.sh 1 real_time  或  ./run_analysis.sh 2 historical

echo "🚀 启动投资组合分析系统..."

# 切换到项目目录
cd "$(dirname "$0")/.."

# 激活虚拟环境
if [ -d ".venv" ]; then
    echo "📦 激活虚拟环境..."
    source .venv/bin/activate
else
    echo "⚠️  虚拟环境不存在，使用系统Python"
fi

# 检查参数
PORTFOLIO=${1:-"1"}
MODE=${2:-"real_time"}

# 验证投资组合编号
if [[ ! "$PORTFOLIO" =~ ^[12]$ ]]; then
    echo "❌ 无效的投资组合编号: $PORTFOLIO"
    echo "💡 请使用 1 或 2"
    echo "📖 使用方法: ./run_analysis.sh [portfolio_number] [real_time|historical]"
    echo "   例如: ./run_analysis.sh 1 real_time"
    echo "        ./run_analysis.sh 2 historical"
    exit 1
fi

# 检查投资组合文件是否存在
PORTFOLIO_FILE="portfolio_monitor/portfolio_list_$PORTFOLIO"
if [ ! -f "$PORTFOLIO_FILE" ]; then
    echo "❌ 投资组合文件不存在: $PORTFOLIO_FILE"
    echo "💡 可用的投资组合文件:"
    for i in 1 2; do
        if [ -f "portfolio_monitor/portfolio_list_$i" ]; then
            echo "   - portfolio_list_$i"
        fi
    done
    exit 1
fi

echo "📊 分析投资组合: $PORTFOLIO"

if [ "$MODE" = "historical" ]; then
    echo "📊 使用历史价格模式..."
    python -c "
from portfolio_monitor.portfolio_analysis_report import main
main(use_real_time_prices=False, portfolio_number=$PORTFOLIO)
"
else
    echo "💰 使用实时价格模式..."
    echo "📡 请确保富途牛牛已启动并开启API接口"
    python portfolio_monitor/portfolio_analysis_report.py --portfolio $PORTFOLIO
fi

echo ""
echo "🎨 生成HTML报告和图表..."
python portfolio_monitor/generate_html_report.py --portfolio $PORTFOLIO

echo ""
echo ""
echo "✅ 投资组合 $PORTFOLIO 分析完成！"
echo "📁 查看生成的文件:"
echo "   - 文本报告: portfolio_monitor/portfolio_report_*.txt"
echo "   - HTML报告: portfolio_monitor/portfolio_${PORTFOLIO}_report_*.html"
echo "   - 图表文件: portfolio_monitor/charts/"
echo ""
echo "🌐 HTML报告已自动在浏览器中打开"
echo ""
echo "💡 使用说明:"
echo "   ./run_analysis.sh 1                    # 分析投资组合1，使用实时价格（推荐）"
echo "   ./run_analysis.sh 2                    # 分析投资组合2，使用实时价格"
echo "   ./run_analysis.sh 1 historical         # 分析投资组合1，使用历史价格（离线模式）"
echo "   ./run_analysis.sh 2 historical         # 分析投资组合2，使用历史价格（离线模式）"
