#!/usr/bin/env python3
"""
投资组合可视化分析
生成投资组合的图表分析
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utilities.complete_font_solution import setup_chinese_fonts
from portfolio_monitor.portfolio_analysis_report import PortfolioAnalyzer

warnings.filterwarnings('ignore')

class PortfolioVisualizer:
    """投资组合可视化器"""
    
    def __init__(self, analyzer: PortfolioAnalyzer):
        self.analyzer = analyzer

        # 设置中文字体
        self.font_prop = setup_chinese_fonts()

        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def create_portfolio_overview_charts(self, output_dir: str = "portfolio_monitor/charts"):
        """创建投资组合概览图表"""
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取分析数据
        portfolio_values, total_value = self.analyzer.calculate_portfolio_weights()
        returns_data = self.analyzer.calculate_returns_and_volatility()
        correlation_matrix = self.analyzer.calculate_correlation_matrix()
        
        # 1. 投资组合权重饼图
        self._plot_portfolio_weights(portfolio_values, output_dir)
        
        # 2. 收益率对比图
        self._plot_returns_comparison(returns_data, output_dir)
        
        # 3. 风险收益散点图
        self._plot_risk_return_scatter(returns_data, portfolio_values, output_dir)
        
        # 4. 相关性热力图
        self._plot_correlation_heatmap(correlation_matrix, output_dir)
        
        # 5. 波动率分析
        self._plot_volatility_analysis(returns_data, output_dir)
        
        print(f"✅ 所有图表已保存到: {output_dir}")
        
    def _plot_portfolio_weights(self, portfolio_values, output_dir):
        """绘制投资组合权重饼图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 准备数据 - 按权重排序
        stocks = []
        stock_labels = []
        weights = []
        values = []

        # 按权重降序排序
        sorted_portfolio = sorted(portfolio_values.items(), key=lambda x: x[1]['weight'], reverse=True)

        for stock_code, info in sorted_portfolio:
            stocks.append(stock_code)
            # 创建更简洁的标签
            stock_name = info['stock_name']
            if len(stock_name) > 6:
                stock_name = stock_name[:6]
            stock_labels.append(f"{stock_code}\n{stock_name}")
            weights.append(info['weight'])
            values.append(info['value'])

        # 权重饼图
        colors = plt.cm.Set3(np.linspace(0, 1, len(stocks)))
        wedges, texts, autotexts = ax1.pie(weights, labels=stock_labels, autopct='%1.1f%%',
                                          colors=colors, startangle=90)
        ax1.set_title('投资组合权重分布', fontsize=14, fontweight='bold', fontproperties=self.font_prop)

        # 设置饼图标签字体
        for text in texts:
            text.set_fontproperties(self.font_prop)
        for autotext in autotexts:
            autotext.set_fontproperties(self.font_prop)

        # 金额柱状图
        bars = ax2.bar(range(len(stocks)), values, color=colors)
        ax2.set_title('持仓金额分布', fontsize=14, fontweight='bold', fontproperties=self.font_prop)
        ax2.set_ylabel('金额 (港币)', fontsize=12, fontproperties=self.font_prop)
        ax2.set_xticks(range(len(stocks)))
        ax2.set_xticklabels(stock_labels, fontproperties=self.font_prop, rotation=45)

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value:,.0f}', ha='center', va='bottom', fontsize=10, fontproperties=self.font_prop)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'portfolio_weights.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def _plot_returns_comparison(self, returns_data, output_dir):
        """绘制收益率对比图"""
        fig, ax = plt.subplots(figsize=(14, 8))

        periods = ['5d', '20d', '60d', '200d']
        period_names = ['5日', '20日', '60日', '200日']

        # 获取股票代码和名称 - 按5日收益率排序
        returns_list = list(returns_data.items())
        sorted_returns = sorted(returns_list, key=lambda x: x[1].get('return_5d', 0), reverse=True)

        stocks = [item[0] for item in sorted_returns]
        stock_names = []
        for stock_code in stocks:
            if stock_code in self.analyzer.latest_prices:
                stock_name = self.analyzer.latest_prices[stock_code]['stock_name']
                # 截取股票名称，避免过长
                if len(stock_name) > 6:
                    stock_name = stock_name[:6]
                stock_names.append(f"{stock_code}\n{stock_name}")
            else:
                stock_names.append(stock_code)

        x = np.arange(len(stocks))
        width = 0.2

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

        for i, (period, name, color) in enumerate(zip(periods, period_names, colors)):
            returns = [returns_data[stock].get(f'return_{period}', 0) for stock in stocks]
            bars = ax.bar(x + i*width, returns, width, label=name, color=color, alpha=0.8)

            # 添加数值标签
            for bar, ret in zip(bars, returns):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -3),
                       f'{ret:.1f}%', ha='center', va='bottom' if height >= 0 else 'top',
                       fontsize=9, fontweight='bold', fontproperties=self.font_prop)

        ax.set_xlabel('股票', fontsize=12, fontproperties=self.font_prop)
        ax.set_ylabel('收益率 (%)', fontsize=12, fontproperties=self.font_prop)
        ax.set_title('多周期收益率对比', fontsize=14, fontweight='bold', fontproperties=self.font_prop)
        ax.set_xticks(x + width * 1.5)
        ax.set_xticklabels(stock_names, fontproperties=self.font_prop)
        ax.legend(prop=self.font_prop)
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'returns_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def _plot_risk_return_scatter(self, returns_data, portfolio_values, output_dir):
        """绘制风险收益散点图"""
        fig, ax = plt.subplots(figsize=(12, 8))

        # 准备数据
        stocks = []
        stock_names = []
        returns_60d = []
        volatilities = []
        weights = []

        for stock_code in returns_data.keys():
            if stock_code in portfolio_values:
                stocks.append(stock_code)
                # 获取股票名称
                if stock_code in self.analyzer.latest_prices:
                    stock_name = self.analyzer.latest_prices[stock_code]['stock_name']
                    if len(stock_name) > 4:
                        stock_name = stock_name[:4]
                    stock_names.append(f"{stock_code}\n{stock_name}")
                else:
                    stock_names.append(stock_code)

                returns_60d.append(returns_data[stock_code].get('return_60d', 0))
                volatilities.append(returns_data[stock_code].get('current_volatility', 0))
                weights.append(portfolio_values[stock_code]['weight'])

        # 创建散点图，气泡大小代表权重
        scatter = ax.scatter(volatilities, returns_60d, s=[w*20 for w in weights],
                           alpha=0.7, c=range(len(stocks)), cmap='viridis')

        # 添加股票名称标签
        for i, stock_name in enumerate(stock_names):
            ax.annotate(stock_name, (volatilities[i], returns_60d[i]),
                       xytext=(5, 5), textcoords='offset points', fontsize=10,
                       fontproperties=self.font_prop)

        ax.set_xlabel('年化波动率 (%)', fontsize=12, fontproperties=self.font_prop)
        ax.set_ylabel('60日收益率 (%)', fontsize=12, fontproperties=self.font_prop)
        ax.set_title('风险收益散点图\n(气泡大小代表投资组合权重)', fontsize=14, fontweight='bold',
                    fontproperties=self.font_prop)
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax.axvline(x=np.mean(volatilities), color='red', linestyle='--', alpha=0.5)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'risk_return_scatter.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def _plot_correlation_heatmap(self, correlation_matrix, output_dir):
        """绘制相关性热力图"""
        if correlation_matrix.empty:
            return

        fig, ax = plt.subplots(figsize=(12, 10))

        # 创建股票名称标签
        stock_labels = []
        for stock_code in correlation_matrix.index:
            if stock_code in self.analyzer.latest_prices:
                stock_name = self.analyzer.latest_prices[stock_code]['stock_name']
                if len(stock_name) > 4:
                    stock_name = stock_name[:4]
                stock_labels.append(f"{stock_code}\n{stock_name}")
            else:
                stock_labels.append(stock_code)

        # 创建热力图
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r',
                   center=0, square=True, linewidths=0.5, cbar_kws={"shrink": .8},
                   fmt='.3f', ax=ax, xticklabels=stock_labels, yticklabels=stock_labels)

        ax.set_title('股票间相关性矩阵', fontsize=14, fontweight='bold', fontproperties=self.font_prop)

        # 设置标签字体
        ax.set_xticklabels(ax.get_xticklabels(), fontproperties=self.font_prop, rotation=45)
        ax.set_yticklabels(ax.get_yticklabels(), fontproperties=self.font_prop, rotation=0)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'correlation_heatmap.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def _plot_volatility_analysis(self, returns_data, output_dir):
        """绘制波动率分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 按波动率排序
        returns_list = list(returns_data.items())
        sorted_volatility = sorted(returns_list, key=lambda x: x[1].get('current_volatility', 0), reverse=True)

        stocks = [item[0] for item in sorted_volatility]
        stock_names = []
        for stock_code in stocks:
            if stock_code in self.analyzer.latest_prices:
                stock_name = self.analyzer.latest_prices[stock_code]['stock_name']
                if len(stock_name) > 4:
                    stock_name = stock_name[:4]
                stock_names.append(f"{stock_code}\n{stock_name}")
            else:
                stock_names.append(stock_code)

        current_vols = [returns_data[stock].get('current_volatility', 0) for stock in stocks]
        vol_percentiles = [returns_data[stock].get('volatility_percentile', 0) for stock in stocks]

        # 当前波动率柱状图
        colors = plt.cm.viridis(np.linspace(0, 1, len(stocks)))
        bars1 = ax1.bar(range(len(stocks)), current_vols, color=colors, alpha=0.8)
        ax1.set_title('当前年化波动率', fontsize=14, fontweight='bold', fontproperties=self.font_prop)
        ax1.set_ylabel('波动率 (%)', fontsize=12, fontproperties=self.font_prop)
        ax1.set_xticks(range(len(stocks)))
        ax1.set_xticklabels(stock_names, fontproperties=self.font_prop, rotation=45)

        # 添加数值标签
        for bar, vol in zip(bars1, current_vols):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{vol:.1f}%', ha='center', va='bottom', fontsize=10, fontproperties=self.font_prop)

        # 波动率历史分位数
        bars2 = ax2.bar(range(len(stocks)), vol_percentiles, color=colors, alpha=0.8)
        ax2.set_title('波动率历史分位数', fontsize=14, fontweight='bold', fontproperties=self.font_prop)
        ax2.set_ylabel('分位数 (%)', fontsize=12, fontproperties=self.font_prop)
        ax2.set_xticks(range(len(stocks)))
        ax2.set_xticklabels(stock_names, fontproperties=self.font_prop, rotation=45)
        ax2.axhline(y=50, color='red', linestyle='--', alpha=0.7, label='中位数')
        ax2.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='80分位')
        ax2.legend(prop=self.font_prop)

        # 添加数值标签
        for bar, perc in zip(bars2, vol_percentiles):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{perc:.1f}%', ha='center', va='bottom', fontsize=10, fontproperties=self.font_prop)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'volatility_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()


def main(portfolio_number: int = 1):
    """主函数"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    portfolio_file = os.path.join(current_dir, f"portfolio_list_{portfolio_number}")

    # 检查投资组合文件是否存在
    if not os.path.exists(portfolio_file):
        print(f"❌ 投资组合文件不存在: {portfolio_file}")
        return

    # 创建分析器
    analyzer = PortfolioAnalyzer(portfolio_file)

    # 加载数据
    if analyzer.load_portfolio() is None:
        print("❌ 无法加载投资组合数据")
        return

    analyzer.load_stock_data_for_portfolio()
    analyzer.get_latest_prices_and_info()

    # 创建可视化器并生成图表
    visualizer = PortfolioVisualizer(analyzer)
    visualizer.create_portfolio_overview_charts()

    print(f"🎨 投资组合 {portfolio_number} 可视化分析完成！")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='生成投资组合可视化图表')
    parser.add_argument('--portfolio', '-p', type=int, choices=[1, 2], default=1,
                        help='选择投资组合编号 (1 或 2), 默认为 1')

    args = parser.parse_args()
    main(portfolio_number=args.portfolio)
