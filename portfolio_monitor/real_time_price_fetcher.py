#!/usr/bin/env python3
"""
实时股价获取器
使用富途API获取实时股价数据，用于计算投资组合的实际价值
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from futu import *
    FUTU_AVAILABLE = True
except ImportError:
    FUTU_AVAILABLE = False
    print("⚠️  富途API不可用，将使用历史数据作为替代")

class RealTimePriceFetcher:
    """实时股价获取器"""
    
    def __init__(self, host='127.0.0.1', port=11111):
        """
        初始化实时股价获取器
        
        Args:
            host: 富途API主机地址
            port: 富途API端口
        """
        self.host = host
        self.port = port
        self.quote_ctx = None
        self.connected = False
        
    def connect(self) -> bool:
        """连接到富途API"""
        if not FUTU_AVAILABLE:
            print("❌ 富途API不可用")
            return False
        
        try:
            self.quote_ctx = OpenQuoteContext(host=self.host, port=self.port)
            self.connected = True
            print("✅ 成功连接到富途API")
            return True
        except Exception as e:
            print(f"❌ 连接富途API失败: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """断开富途API连接"""
        if self.quote_ctx:
            self.quote_ctx.close()
            self.connected = False
            print("🔌 已断开富途API连接")
    
    def get_real_time_price(self, stock_code: str) -> Optional[Dict]:
        """
        获取单只股票的实时价格
        
        Args:
            stock_code: 股票代码（不含HK.前缀）
            
        Returns:
            Dict: 包含实时价格信息的字典，如果失败返回None
        """
        if not self.connected:
            print("❌ 未连接到富途API")
            return None
        
        try:
            futu_code = f"HK.{stock_code}"
            ret, data = self.quote_ctx.get_market_snapshot([futu_code])
            
            if ret == RET_OK and not data.empty:
                row = data.iloc[0]

                # 调试信息（可选）
                # print(f"可用列: {list(data.columns)}")

                return {
                    'stock_code': stock_code,
                    'futu_code': futu_code,
                    'current_price': row.get('last_price', row.get('cur_price', np.nan)),
                    'open_price': row.get('open_price', np.nan),
                    'high_price': row.get('high_price', np.nan),
                    'low_price': row.get('low_price', np.nan),
                    'prev_close': row.get('prev_close_price', row.get('pre_close_price', np.nan)),
                    'change_val': row.get('change_val', row.get('change_price', 0)),
                    'change_rate': row.get('change_rate', row.get('change_percent', 0)),
                    'volume': row.get('volume', np.nan),
                    'turnover': row.get('turnover', np.nan),
                    'market_val': row.get('market_val', np.nan),
                    'pe_ratio': row.get('pe_ratio', np.nan),
                    'pb_ratio': row.get('pb_ratio', np.nan),
                    'update_time': row.get('update_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    'stock_name': row.get('name', row.get('stock_name', f'股票{stock_code}')),
                    'fetch_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                print(f"❌ 获取 {futu_code} 实时数据失败: {data}")
                return None
                
        except Exception as e:
            print(f"❌ 获取 {stock_code} 实时价格时出错: {e}")
            return None
    
    def get_batch_real_time_prices(self, stock_codes: List[str]) -> Dict[str, Dict]:
        """
        批量获取多只股票的实时价格
        
        Args:
            stock_codes: 股票代码列表（不含HK.前缀）
            
        Returns:
            Dict: 股票代码到价格信息的映射
        """
        if not self.connected:
            print("❌ 未连接到富途API")
            return {}
        
        results = {}
        
        try:
            # 构建富途代码列表
            futu_codes = [f"HK.{code}" for code in stock_codes]
            
            # 批量获取快照数据
            ret, data = self.quote_ctx.get_market_snapshot(futu_codes)
            
            if ret == RET_OK and not data.empty:
                # 打印列名以便调试
                print(f"批量获取可用列: {list(data.columns)}")

                for _, row in data.iterrows():
                    stock_code = row['code'].replace('HK.', '')
                    results[stock_code] = {
                        'stock_code': stock_code,
                        'futu_code': row['code'],
                        'current_price': row.get('last_price', row.get('cur_price', np.nan)),
                        'open_price': row.get('open_price', np.nan),
                        'high_price': row.get('high_price', np.nan),
                        'low_price': row.get('low_price', np.nan),
                        'prev_close': row.get('prev_close_price', row.get('pre_close_price', np.nan)),
                        'change_val': row.get('change_val', row.get('change_price', 0)),
                        'change_rate': row.get('change_rate', row.get('change_percent', 0)),
                        'volume': row.get('volume', np.nan),
                        'turnover': row.get('turnover', np.nan),
                        'market_val': row.get('market_val', np.nan),
                        'pe_ratio': row.get('pe_ratio', np.nan),
                        'pb_ratio': row.get('pb_ratio', np.nan),
                        'update_time': row.get('update_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                        'stock_name': row.get('name', row.get('stock_name', f'股票{stock_code}')),
                        'fetch_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                
                print(f"✅ 成功获取 {len(results)} 只股票的实时价格")
            else:
                print(f"❌ 批量获取实时数据失败: {data}")
                
        except Exception as e:
            print(f"❌ 批量获取实时价格时出错: {e}")
        
        return results
    
    def get_portfolio_real_time_values(self, portfolio_file: str) -> Dict:
        """
        获取投资组合的实时价值
        
        Args:
            portfolio_file: 投资组合文件路径
            
        Returns:
            Dict: 包含实时价值信息的字典
        """
        try:
            # 读取投资组合
            portfolio_df = pd.read_csv(portfolio_file, sep=r'\s+', engine='python')
            portfolio_df['code_clean'] = portfolio_df['code'].str.replace('HK.', '', regex=False)
            
            stock_codes = portfolio_df['code_clean'].tolist()
            
            # 获取实时价格
            real_time_prices = self.get_batch_real_time_prices(stock_codes)
            
            # 计算投资组合价值
            portfolio_values = {}
            total_value = 0
            
            for _, row in portfolio_df.iterrows():
                stock_code = row['code_clean']
                shares = row['shares_held']
                
                if stock_code in real_time_prices:
                    price_info = real_time_prices[stock_code]
                    current_price = price_info['current_price']
                    value = shares * current_price
                    
                    portfolio_values[stock_code] = {
                        'shares': shares,
                        'current_price': current_price,
                        'value': value,
                        'stock_name': price_info['stock_name'],
                        'change_rate': price_info['change_rate'],
                        'change_val': price_info['change_val'],
                        'prev_close': price_info['prev_close'],
                        'update_time': price_info['update_time'],
                        'pe_ratio': price_info['pe_ratio']
                    }
                    total_value += value
                else:
                    print(f"⚠️  未能获取 {stock_code} 的实时价格")
            
            # 计算权重
            for stock_code in portfolio_values:
                portfolio_values[stock_code]['weight'] = portfolio_values[stock_code]['value'] / total_value * 100
            
            return {
                'portfolio_values': portfolio_values,
                'total_value': total_value,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': 'real_time_futu_api'
            }
            
        except Exception as e:
            print(f"❌ 获取投资组合实时价值失败: {e}")
            return {}
    
    def save_real_time_snapshot(self, portfolio_file: str, output_file: str = None) -> str:
        """
        保存投资组合实时快照到文件
        
        Args:
            portfolio_file: 投资组合文件路径
            output_file: 输出文件路径，如果为None则自动生成
            
        Returns:
            str: 保存的文件路径
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"portfolio_monitor/real_time_snapshot_{timestamp}.json"
        
        try:
            real_time_data = self.get_portfolio_real_time_values(portfolio_file)
            
            if real_time_data:
                import json
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(real_time_data, f, ensure_ascii=False, indent=2, default=str)
                
                print(f"✅ 实时快照已保存到: {output_file}")
                return output_file
            else:
                print("❌ 无法获取实时数据")
                return ""
                
        except Exception as e:
            print(f"❌ 保存实时快照失败: {e}")
            return ""


def main():
    """主函数 - 测试实时价格获取"""
    print("🔄 测试实时股价获取...")
    
    # 创建价格获取器
    fetcher = RealTimePriceFetcher()
    
    # 连接API
    if not fetcher.connect():
        print("❌ 无法连接到富途API，请确保富途牛牛已启动并开启API")
        return
    
    try:
        # 测试单只股票
        print("\n📊 测试单只股票价格获取:")
        price_info = fetcher.get_real_time_price("02333")  # 长城汽车
        if price_info:
            print(f"  {price_info['stock_name']} ({price_info['stock_code']})")
            print(f"  当前价格: {price_info['current_price']:.2f} 港币")
            print(f"  涨跌幅: {price_info['change_rate']:+.2f}%")
            print(f"  更新时间: {price_info['update_time']}")
        
        # 测试投资组合
        print("\n💼 测试投资组合实时价值:")
        portfolio_file = "portfolio_monitor/portfolio_list_1"
        if os.path.exists(portfolio_file):
            portfolio_data = fetcher.get_portfolio_real_time_values(portfolio_file)
            if portfolio_data:
                print(f"  投资组合总价值: {portfolio_data['total_value']:,.2f} 港币")
                print(f"  数据更新时间: {portfolio_data['update_time']}")
                
                # 保存快照
                snapshot_file = fetcher.save_real_time_snapshot(portfolio_file)
                if snapshot_file:
                    print(f"  快照已保存: {snapshot_file}")
        else:
            print(f"  ❌ 投资组合文件不存在: {portfolio_file}")
    
    finally:
        # 断开连接
        fetcher.disconnect()


if __name__ == "__main__":
    main()
