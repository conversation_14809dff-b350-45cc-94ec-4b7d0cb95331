#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化对股价预测性分析

分析港股通持股比例变化对后续N天股价变化的先行指导作用
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from scipy import stats
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入交易日历工具
from utilities.utils import get_futu_trading_calendar

class HSGTPredictiveAnalyzer:
    """港股通持股比例预测性分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化分析器
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.df = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载数据"""
        print(f"正在加载数据: {self.data_path}")
        try:
            self.df = pd.read_parquet(self.data_path)
            self.df['time_key'] = pd.to_datetime(self.df['time_key'])
            print(f"数据加载成功，共 {len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def prepare_analysis_data(self, min_data_points: int = 30):
        """
        准备分析数据
        
        Args:
            min_data_points: 每只股票最少需要的数据点数
        """
        print("正在准备分析数据...")
        
        # 过滤有港股通数据的记录
        hsgt_data = self.df[self.df['hsgt_holding_ratio'].notna()].copy()
        
        if hsgt_data.empty:
            print("未找到港股通持股数据")
            return pd.DataFrame()
        
        # 按股票分组，只保留数据点足够的股票
        valid_stocks = []
        for code in hsgt_data['code'].unique():
            stock_data = hsgt_data[hsgt_data['code'] == code]
            if len(stock_data) >= min_data_points:
                valid_stocks.append(code)
        
        print(f"找到 {len(valid_stocks)} 只股票有足够的港股通数据（>={min_data_points}个数据点）")
        
        # 过滤数据
        analysis_data = hsgt_data[hsgt_data['code'].isin(valid_stocks)].copy()
        analysis_data = analysis_data.sort_values(['code', 'time_key'])
        
        return analysis_data
    
    def calculate_hsgt_changes(self, data: pd.DataFrame, lookback_days_list: list = [1, 3, 5, 10, 20]):
        """
        计算港股通持股比例变化

        Args:
            data: 分析数据
            lookback_days_list: 回看天数列表
        """
        print(f"计算港股通持股比例变化（回看天数: {lookback_days_list}）...")

        results = []

        for code in data['code'].unique():
            stock_data = data[data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')

            # 计算不同时间窗口的持股比例变化
            for days in lookback_days_list:
                # 绝对变化
                stock_data[f'hsgt_ratio_change_{days}d'] = (
                    stock_data['hsgt_holding_ratio'] -
                    stock_data['hsgt_holding_ratio'].shift(days)
                )

                # 百分比变化
                stock_data[f'hsgt_ratio_pct_change_{days}d'] = (
                    (stock_data['hsgt_holding_ratio'] /
                     stock_data['hsgt_holding_ratio'].shift(days) - 1) * 100
                )

            results.append(stock_data)

        return pd.concat(results, ignore_index=True)
    
    def calculate_future_returns(self, data: pd.DataFrame, forward_days_list: list = [1, 3, 5, 10, 20]):
        """
        计算未来收益率
        
        Args:
            data: 包含港股通变化的数据
            forward_days_list: 前瞻天数列表
        """
        print(f"计算未来收益率（前瞻天数: {forward_days_list}）...")
        
        results = []
        
        for code in data['code'].unique():
            stock_data = data[data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            # 计算未来收益率
            for days in forward_days_list:
                # 价格变化
                stock_data[f'price_change_{days}d'] = (
                    stock_data['close'].shift(-days) - stock_data['close']
                )
                
                # 收益率
                stock_data[f'return_{days}d'] = (
                    (stock_data['close'].shift(-days) / stock_data['close'] - 1) * 100
                )
                
                # 对数收益率
                stock_data[f'log_return_{days}d'] = (
                    np.log(stock_data['close'].shift(-days) / stock_data['close']) * 100
                )
            
            results.append(stock_data)
        
        return pd.concat(results, ignore_index=True)
    
    def perform_correlation_analysis(self, data: pd.DataFrame, lookback_days_list: list = [1, 3, 5, 10, 20],
                                   forward_days_list: list = [1, 3, 5, 10, 20]):
        """
        执行相关性分析

        Args:
            data: 完整的分析数据
            lookback_days_list: 港股通变化回看天数列表
            forward_days_list: 未来收益率前瞻天数
        """
        print("执行相关性分析...")

        # 准备分析用的列名 - 包含所有时间窗口
        hsgt_change_cols = []
        for days in lookback_days_list:
            hsgt_change_cols.extend([
                f'hsgt_ratio_change_{days}d',
                f'hsgt_ratio_pct_change_{days}d'
            ])
        
        return_cols = []
        for days in forward_days_list:
            return_cols.extend([
                f'return_{days}d',
                f'log_return_{days}d'
            ])
        
        # 移除缺失值和无穷大值
        analysis_cols = hsgt_change_cols + return_cols
        clean_data = data[analysis_cols].copy()

        # 替换无穷大值为NaN
        clean_data = clean_data.replace([np.inf, -np.inf], np.nan)

        # 移除NaN值
        clean_data = clean_data.dropna()

        if clean_data.empty:
            print("警告: 清理后的数据为空")
            return {}

        print(f"有效数据点: {len(clean_data)}")

        # 进一步检查数据质量
        for col in analysis_cols:
            if col in clean_data.columns:
                # 移除异常值（超过5个标准差的值）
                mean_val = clean_data[col].mean()
                std_val = clean_data[col].std()
                if std_val > 0:
                    outlier_mask = np.abs(clean_data[col] - mean_val) > 5 * std_val
                    if outlier_mask.sum() > 0:
                        print(f"移除 {col} 列中的 {outlier_mask.sum()} 个异常值")
                        clean_data = clean_data[~outlier_mask]

        if clean_data.empty:
            print("警告: 移除异常值后数据为空")
            return {}

        print(f"最终有效数据点: {len(clean_data)}")

        # 计算相关性矩阵
        correlation_matrix = clean_data.corr()

        # 提取港股通变化与未来收益的相关性
        correlations = {}
        for hsgt_col in hsgt_change_cols:
            if hsgt_col not in clean_data.columns:
                continue

            correlations[hsgt_col] = {}
            for return_col in return_cols:
                if return_col not in clean_data.columns:
                    continue

                # 检查数据是否有效
                x_data = clean_data[hsgt_col]
                y_data = clean_data[return_col]

                # 确保数据没有NaN或无穷大
                valid_mask = np.isfinite(x_data) & np.isfinite(y_data)
                x_clean = x_data[valid_mask]
                y_clean = y_data[valid_mask]

                if len(x_clean) < 10:  # 至少需要10个数据点
                    continue

                # 检查是否有变化（标准差不为0）
                if x_clean.std() == 0 or y_clean.std() == 0:
                    continue

                try:
                    # 计算显著性检验
                    corr_coef, p_value = stats.pearsonr(x_clean, y_clean)

                    # 检查结果是否有效
                    if np.isfinite(corr_coef) and np.isfinite(p_value):
                        correlations[hsgt_col][return_col] = {
                            'correlation': corr_coef,
                            'p_value': p_value,
                            'significant': p_value < 0.05,
                            'sample_size': len(x_clean)
                        }
                except Exception as e:
                    print(f"计算相关性时出错 {hsgt_col} vs {return_col}: {e}")
                    continue
        
        return correlations, correlation_matrix, clean_data

    def analyze_same_period_correlation(self, correlations: dict, lookback_days_list: list, forward_days_list: list):
        """分析相同时间窗口的相关性（N天港股通变化 vs N天收益）"""
        print("\n" + "="*60)
        print("    Same Period Analysis: N-Day HSGT Change vs N-Day Returns")
        print("="*60)

        same_period_results = []

        for days in lookback_days_list:
            if days in forward_days_list:
                hsgt_abs_col = f'hsgt_ratio_change_{days}d'
                hsgt_pct_col = f'hsgt_ratio_pct_change_{days}d'
                return_col = f'return_{days}d'
                log_return_col = f'log_return_{days}d'

                # 检查数据是否存在
                if (hsgt_abs_col in correlations and
                    return_col in correlations[hsgt_abs_col]):

                    abs_corr = correlations[hsgt_abs_col][return_col]
                    same_period_results.append({
                        'days': days,
                        'hsgt_type': 'Absolute Change',
                        'return_type': 'Return',
                        'correlation': abs_corr['correlation'],
                        'p_value': abs_corr['p_value'],
                        'significant': abs_corr['significant']
                    })

                if (hsgt_pct_col in correlations and
                    return_col in correlations[hsgt_pct_col]):

                    pct_corr = correlations[hsgt_pct_col][return_col]
                    same_period_results.append({
                        'days': days,
                        'hsgt_type': 'Percentage Change',
                        'return_type': 'Return',
                        'correlation': pct_corr['correlation'],
                        'p_value': pct_corr['p_value'],
                        'significant': pct_corr['significant']
                    })

        if same_period_results:
            same_period_df = pd.DataFrame(same_period_results)
            same_period_df = same_period_df.sort_values('correlation', key=abs, ascending=False)

            print("\n🎯 Same Period Correlation Results:")
            for _, row in same_period_df.iterrows():
                significance = "***" if row['p_value'] < 0.001 else "**" if row['p_value'] < 0.01 else "*" if row['p_value'] < 0.05 else ""
                print(f"   {row['days']}D {row['hsgt_type']} vs {row['days']}D {row['return_type']}: "
                      f"r={row['correlation']:.4f} (p={row['p_value']:.4f}){significance}")

            return same_period_df
        else:
            print("No same period correlation data found.")
            return pd.DataFrame()

    def generate_correlation_report(self, correlations: dict, lookback_days_list: list, forward_days_list: list):
        """生成相关性分析报告"""
        print("\n" + "="*80)
        print("    HSGT Holdings Change vs Future Returns Correlation Report")
        print("="*80)
        
        # 创建汇总表
        summary_data = []
        
        for hsgt_col, return_correlations in correlations.items():
            for return_col, stats_data in return_correlations.items():
                # 解析天数和收益类型
                if 'return_' in return_col:
                    parts = return_col.split('_')
                    if len(parts) >= 2:
                        # 处理 log_return_Xd 格式
                        if return_col.startswith('log_return_'):
                            return_type = 'log_return'
                            days_part = parts[2] if len(parts) > 2 else parts[1]
                        # 处理 return_Xd 格式
                        else:
                            return_type = 'return'
                            days_part = parts[1]

                        # 提取数字部分
                        days_str = days_part.replace('d', '')
                        try:
                            days = int(days_str)
                        except ValueError:
                            print(f"警告: 无法解析天数 '{days_part}' 从列名 '{return_col}'")
                            continue
                    else:
                        continue
                else:
                    continue

                summary_data.append({
                    'HSGT指标': hsgt_col,
                    '未来收益类型': return_type,
                    '前瞻天数': days,
                    '相关系数': stats_data['correlation'],
                    'P值': stats_data['p_value'],
                    '显著性': '是' if stats_data['significant'] else '否',
                    '样本数量': stats_data['sample_size']
                })
        
        summary_df = pd.DataFrame(summary_data)
        
        # 按相关系数绝对值排序
        summary_df['相关系数绝对值'] = summary_df['相关系数'].abs()
        summary_df = summary_df.sort_values('相关系数绝对值', ascending=False)
        
        # 显示最强相关性（前20个）
        print("\n🔥 Strongest Correlations (Top 20):")
        top_correlations = summary_df.head(20)

        for _, row in top_correlations.iterrows():
            significance = "***" if row['P值'] < 0.001 else "**" if row['P值'] < 0.01 else "*" if row['P值'] < 0.05 else ""
            print(f"   {row['HSGT指标']} -> {row['未来收益类型']}_{row['前瞻天数']}D: "
                  f"r={row['相关系数']:.4f} (p={row['P值']:.4f}){significance}")

        # 按前瞻天数分组分析
        print(f"\n📊 Average Correlation by Forward Days:")
        for days in forward_days_list:
            day_data = summary_df[summary_df['前瞻天数'] == days]
            if not day_data.empty:
                avg_corr = day_data['相关系数绝对值'].mean()
                significant_count = day_data['显著性'].value_counts().get('是', 0)
                total_count = len(day_data)
                print(f"   {days}D: avg_abs_corr={avg_corr:.4f}, "
                      f"significant={significant_count}/{total_count}")

        # 分析相同时间窗口的相关性
        same_period_df = self.analyze_same_period_correlation(correlations, lookback_days_list, forward_days_list)

        return summary_df, same_period_df

    def create_strategy_signals(self, data: pd.DataFrame, lookback_days: int = 5):
        """
        创建基于港股通变化的交易信号

        Args:
            data: 包含港股通变化的数据
            lookback_days: 回看天数
        """
        print("创建交易信号...")

        results = []

        for code in data['code'].unique():
            stock_data = data[data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')

            if len(stock_data) < lookback_days + 10:  # 确保有足够数据
                continue

            # 计算港股通变化的分位数阈值
            hsgt_change_col = f'hsgt_ratio_change_{lookback_days}d'
            if hsgt_change_col not in stock_data.columns:
                continue

            # 移除缺失值
            valid_data = stock_data[stock_data[hsgt_change_col].notna()].copy()
            if len(valid_data) < 20:
                continue

            # 计算阈值（使用历史数据的分位数）
            threshold_high = valid_data[hsgt_change_col].quantile(0.8)  # 前20%
            threshold_low = valid_data[hsgt_change_col].quantile(0.2)   # 后20%

            # 生成信号
            valid_data['signal'] = 0  # 0: 无信号, 1: 买入, -1: 卖出
            valid_data.loc[valid_data[hsgt_change_col] >= threshold_high, 'signal'] = 1
            valid_data.loc[valid_data[hsgt_change_col] <= threshold_low, 'signal'] = -1

            results.append(valid_data)

        if results:
            return pd.concat(results, ignore_index=True)
        else:
            return pd.DataFrame()

def main():
    """主函数"""
    print("="*80)
    print("    HSGT Holdings Change Predictive Analysis")
    print("="*80)

    # 数据文件路径
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')

    if not os.path.exists(data_path):
        print(f"Error: Data file not found {data_path}")
        return

    # 创建分析器
    analyzer = HSGTPredictiveAnalyzer(data_path)

    # 加载数据
    if not analyzer.load_data():
        return

    # 准备分析数据
    analysis_data = analyzer.prepare_analysis_data(min_data_points=50)
    if analysis_data.empty:
        return

    # 分析参数 - 扩展为多个时间窗口
    lookback_days_list = [1, 3, 5, 10, 20]  # 港股通变化回看天数
    forward_days_list = [1, 3, 5, 10, 20]   # 未来收益前瞻天数

    # 计算港股通变化
    data_with_hsgt_changes = analyzer.calculate_hsgt_changes(analysis_data, lookback_days_list)

    # 计算未来收益
    complete_data = analyzer.calculate_future_returns(data_with_hsgt_changes, forward_days_list)

    # 执行相关性分析
    correlations, correlation_matrix, clean_data = analyzer.perform_correlation_analysis(
        complete_data, lookback_days_list, forward_days_list
    )

    # 生成报告
    summary_df, same_period_df = analyzer.generate_correlation_report(correlations, lookback_days_list, forward_days_list)

    # 创建交易信号（使用5天作为默认）
    signal_data = analyzer.create_strategy_signals(complete_data, 5)

    # 保存结果
    output_dir = os.path.join(os.path.dirname(__file__), 'analysis_results')
    os.makedirs(output_dir, exist_ok=True)

    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 保存相关性汇总
    summary_path = os.path.join(output_dir, f'hsgt_correlation_summary_{current_date}.csv')
    summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig')
    print(f"\n💾 Correlation summary saved to: {summary_path}")

    # 保存相同时间窗口分析结果
    if not same_period_df.empty:
        same_period_path = os.path.join(output_dir, f'hsgt_same_period_analysis_{current_date}.csv')
        same_period_df.to_csv(same_period_path, index=False, encoding='utf-8-sig')
        print(f"💾 Same period analysis saved to: {same_period_path}")

    # 保存完整数据
    complete_data_path = os.path.join(output_dir, f'hsgt_complete_analysis_data_{current_date}.csv')
    complete_data.to_csv(complete_data_path, index=False, encoding='utf-8-sig')
    print(f"💾 Complete analysis data saved to: {complete_data_path}")

    # 保存交易信号数据
    if not signal_data.empty:
        signal_path = os.path.join(output_dir, f'hsgt_trading_signals_{current_date}.csv')
        signal_data.to_csv(signal_path, index=False, encoding='utf-8-sig')
        print(f"💾 Trading signals saved to: {signal_path}")

    print("="*80)

if __name__ == "__main__":
    main()
