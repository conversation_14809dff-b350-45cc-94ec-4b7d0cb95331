#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通优化单股票策略

每天买入港股通比例增加最大的股票，但必须超过指定阈值
结合了单股票集中投资的优势和阈值过滤的风险控制
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTOptimizedSingleStockStrategy:
    """港股通优化单股票策略"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.data = None
        
    def load_data(self, data_path: str):
        """加载数据"""
        try:
            self.data = pd.read_parquet(data_path)
            self.data['time_key'] = pd.to_datetime(self.data['time_key'])
            self.data = self.data[self.data['hsgt_holding_ratio'].notna()].copy()
            print(f"Data loaded: {len(self.data)} records with HSGT data")
            return True
        except Exception as e:
            print(f"Failed to load data: {e}")
            return False
    
    def calculate_hsgt_changes(self):
        """计算港股通变化"""
        results = []
        
        for code in self.data['code'].unique():
            stock_data = self.data[self.data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            if len(stock_data) < 2:
                continue
            
            # 计算1天港股通变化
            stock_data['hsgt_ratio_change_1d'] = stock_data['hsgt_holding_ratio'].diff()
            
            # 计算次日收益率
            stock_data['next_day_return'] = stock_data['close'].shift(-1) / stock_data['close'] - 1
            
            results.append(stock_data)
        
        self.data_with_changes = pd.concat(results, ignore_index=True)
        return self.data_with_changes
    
    def optimized_single_stock_backtest(self, threshold: float = 0.24):
        """
        优化单股票策略回测
        
        Args:
            threshold: HSGT变化阈值，只有超过此阈值才考虑买入
        """
        print(f"Testing optimized single stock strategy with {threshold}% threshold...")
        
        data = self.data_with_changes.copy()
        data = data.sort_values(['time_key', 'code'])
        
        # 初始化
        portfolio_value = self.initial_capital
        cash = self.initial_capital
        trades = []
        portfolio_history = []
        
        trading_dates = sorted(data['time_key'].unique())[:-1]  # 排除最后一天
        
        for current_date in trading_dates:
            current_data = data[data['time_key'] == current_date]
            
            # 找到符合阈值条件的股票
            valid_stocks = current_data[
                (current_data['hsgt_ratio_change_1d'].notna()) &
                (current_data['next_day_return'].notna()) &
                (current_data['hsgt_ratio_change_1d'] >= threshold)  # 阈值过滤
            ].copy()
            
            if valid_stocks.empty:
                # 没有符合条件的股票，持有现金
                portfolio_history.append({
                    'date': current_date,
                    'portfolio_value': portfolio_value,
                    'daily_return': 0,
                    'position_type': 'CASH',
                    'selected_stock': None,
                    'hsgt_change': 0,
                    'stock_return': 0
                })
                continue
            
            # 选择HSGT变化最大的股票
            best_stock = valid_stocks.loc[valid_stocks['hsgt_ratio_change_1d'].idxmax()]
            
            # 全仓买入
            stock_return = best_stock['next_day_return']
            new_portfolio_value = portfolio_value * (1 + stock_return)
            daily_return = stock_return * 100
            
            # 记录交易
            trades.append({
                'date': current_date,
                'code': best_stock['code'],
                'name': best_stock.get('name', ''),
                'entry_price': best_stock['close'],
                'hsgt_change': best_stock['hsgt_ratio_change_1d'],
                'next_day_return': stock_return,
                'portfolio_value_before': portfolio_value,
                'portfolio_value_after': new_portfolio_value,
                'pnl': new_portfolio_value - portfolio_value
            })
            
            # 记录组合历史
            portfolio_history.append({
                'date': current_date,
                'portfolio_value': new_portfolio_value,
                'daily_return': daily_return,
                'position_type': 'STOCK',
                'selected_stock': best_stock['code'],
                'hsgt_change': best_stock['hsgt_ratio_change_1d'],
                'stock_return': stock_return * 100
            })
            
            portfolio_value = new_portfolio_value
        
        self.trades_df = pd.DataFrame(trades)
        self.portfolio_df = pd.DataFrame(portfolio_history)
        
        return self.trades_df, self.portfolio_df
    
    def calculate_performance_metrics(self):
        """计算绩效指标"""
        if self.portfolio_df.empty:
            return {}
        
        portfolio_values = self.portfolio_df['portfolio_value'].values
        daily_returns = self.portfolio_df['daily_return'].values / 100
        
        # 基本指标
        total_return = (portfolio_values[-1] / self.initial_capital - 1) * 100
        trading_days = len(portfolio_values)
        annualized_return = ((portfolio_values[-1] / self.initial_capital) ** (252 / trading_days) - 1) * 100
        
        # 风险指标
        volatility = np.std(daily_returns) * np.sqrt(252) * 100
        sharpe_ratio = (annualized_return - 3) / volatility if volatility > 0 else 0
        
        # 最大回撤
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown) * 100
        
        # 交易统计
        if not self.trades_df.empty:
            total_trades = len(self.trades_df)
            winning_trades = len(self.trades_df[self.trades_df['next_day_return'] > 0])
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            avg_return_per_trade = self.trades_df['next_day_return'].mean() * 100
            avg_hsgt_change = self.trades_df['hsgt_change'].mean()
        else:
            total_trades = 0
            win_rate = 0
            avg_return_per_trade = 0
            avg_hsgt_change = 0
        
        # 持仓统计
        stock_days = len(self.portfolio_df[self.portfolio_df['position_type'] == 'STOCK'])
        cash_days = len(self.portfolio_df[self.portfolio_df['position_type'] == 'CASH'])
        total_days = len(self.portfolio_df)
        
        return {
            'total_return_pct': total_return,
            'annualized_return_pct': annualized_return,
            'volatility_pct': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'total_trades': total_trades,
            'win_rate_pct': win_rate,
            'avg_return_per_trade_pct': avg_return_per_trade,
            'avg_hsgt_change': avg_hsgt_change,
            'stock_days': stock_days,
            'cash_days': cash_days,
            'stock_days_pct': stock_days / total_days * 100,
            'final_value': portfolio_values[-1]
        }
    
    def test_multiple_thresholds(self):
        """测试多个阈值"""
        print("\n" + "="*80)
        print("    Testing Multiple Thresholds for Optimized Single Stock Strategy")
        print("="*80)
        
        # 测试不同阈值
        thresholds = [0.0, 0.1, 0.2, 0.24, 0.3, 0.5, 0.75, 1.0, 1.5, 2.0]
        
        results = []
        
        for threshold in thresholds:
            trades_df, portfolio_df = self.optimized_single_stock_backtest(threshold)
            metrics = self.calculate_performance_metrics()
            
            result = {
                'Threshold': threshold,
                **metrics
            }
            results.append(result)
            
            print(f"Threshold {threshold:4.1f}%: "
                  f"Return {metrics['total_return_pct']:8.1f}%, "
                  f"Trades {metrics['total_trades']:3.0f}, "
                  f"Stock Days {metrics['stock_days_pct']:5.1f}%, "
                  f"Sharpe {metrics['sharpe_ratio']:5.2f}")
        
        return pd.DataFrame(results)
    
    def plot_threshold_comparison(self, results_df, output_dir):
        """绘制阈值比较图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Optimized Single Stock Strategy - Threshold Analysis', fontsize=16, fontweight='bold')
        
        thresholds = results_df['Threshold'].values
        
        # 1. 总收益率
        ax1 = axes[0, 0]
        ax1.plot(thresholds, results_df['total_return_pct'], 'bo-', linewidth=2, markersize=6)
        ax1.set_title('Total Return vs Threshold')
        ax1.set_xlabel('HSGT Change Threshold (%)')
        ax1.set_ylabel('Total Return (%)')
        ax1.grid(True, alpha=0.3)
        
        # 标注最优点
        best_idx = results_df['total_return_pct'].idxmax()
        best_threshold = results_df.iloc[best_idx]['Threshold']
        best_return = results_df.iloc[best_idx]['total_return_pct']
        ax1.annotate(f'Best: {best_threshold}%\n{best_return:.1f}%', 
                    xy=(best_threshold, best_return), xytext=(best_threshold+0.3, best_return+50),
                    arrowprops=dict(arrowstyle='->', color='red', lw=2),
                    fontsize=10, fontweight='bold', color='red')
        
        # 2. 夏普比率
        ax2 = axes[0, 1]
        ax2.plot(thresholds, results_df['sharpe_ratio'], 'go-', linewidth=2, markersize=6)
        ax2.set_title('Sharpe Ratio vs Threshold')
        ax2.set_xlabel('HSGT Change Threshold (%)')
        ax2.set_ylabel('Sharpe Ratio')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=1.0, color='red', linestyle='--', alpha=0.7)
        
        # 3. 交易次数
        ax3 = axes[0, 2]
        ax3.plot(thresholds, results_df['total_trades'], 'ro-', linewidth=2, markersize=6)
        ax3.set_title('Number of Trades vs Threshold')
        ax3.set_xlabel('HSGT Change Threshold (%)')
        ax3.set_ylabel('Total Trades')
        ax3.grid(True, alpha=0.3)
        
        # 4. 持仓天数比例
        ax4 = axes[1, 0]
        ax4.plot(thresholds, results_df['stock_days_pct'], 'mo-', linewidth=2, markersize=6)
        ax4.set_title('Stock Holding Days vs Threshold')
        ax4.set_xlabel('HSGT Change Threshold (%)')
        ax4.set_ylabel('Stock Days (%)')
        ax4.grid(True, alpha=0.3)
        
        # 5. 胜率
        ax5 = axes[1, 1]
        ax5.plot(thresholds, results_df['win_rate_pct'], 'co-', linewidth=2, markersize=6)
        ax5.set_title('Win Rate vs Threshold')
        ax5.set_xlabel('HSGT Change Threshold (%)')
        ax5.set_ylabel('Win Rate (%)')
        ax5.grid(True, alpha=0.3)
        ax5.axhline(y=50, color='red', linestyle='--', alpha=0.7)
        
        # 6. 收益率 vs 夏普比率散点图
        ax6 = axes[1, 2]
        scatter = ax6.scatter(results_df['total_return_pct'], results_df['sharpe_ratio'], 
                             c=results_df['Threshold'], s=100, cmap='viridis', alpha=0.8)
        ax6.set_title('Return vs Sharpe (Color = Threshold)')
        ax6.set_xlabel('Total Return (%)')
        ax6.set_ylabel('Sharpe Ratio')
        ax6.grid(True, alpha=0.3)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax6)
        cbar.set_label('Threshold (%)')
        
        # 标注关键点
        for i, row in results_df.iterrows():
            if row['Threshold'] in [0.0, best_threshold, 1.0]:
                ax6.annotate(f"{row['Threshold']:.1f}%", 
                            xy=(row['total_return_pct'], row['sharpe_ratio']), 
                            xytext=(5, 5), textcoords='offset points',
                            fontsize=9, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(output_dir, 'optimized_single_stock_threshold_analysis.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n📊 Threshold analysis chart saved: {filename}")
        plt.show()

def main():
    """主函数"""
    print("="*80)
    print("    HSGT Optimized Single Stock Strategy with Threshold")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    output_dir = os.path.join(script_dir, 'optimized_single_stock_results')
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建策略实例
    strategy = HSGTOptimizedSingleStockStrategy(initial_capital=1000000)
    
    # 加载数据
    if not strategy.load_data(data_path):
        return
    
    # 计算港股通变化
    strategy.calculate_hsgt_changes()
    
    # 测试多个阈值
    results_df = strategy.test_multiple_thresholds()
    
    # 找出最优策略
    best_return_idx = results_df['total_return_pct'].idxmax()
    best_sharpe_idx = results_df['sharpe_ratio'].idxmax()
    
    print(f"\n{'='*80}")
    print("    Optimal Strategy Analysis")
    print(f"{'='*80}")
    
    best_return = results_df.iloc[best_return_idx]
    print(f"🏆 Highest Return Strategy:")
    print(f"   Threshold: {best_return['Threshold']:.1f}%")
    print(f"   Total Return: {best_return['total_return_pct']:.2f}%")
    print(f"   Annualized Return: {best_return['annualized_return_pct']:.2f}%")
    print(f"   Sharpe Ratio: {best_return['sharpe_ratio']:.2f}")
    print(f"   Total Trades: {best_return['total_trades']:.0f}")
    print(f"   Stock Days: {best_return['stock_days_pct']:.1f}%")
    print(f"   Win Rate: {best_return['win_rate_pct']:.1f}%")
    
    best_sharpe = results_df.iloc[best_sharpe_idx]
    print(f"\n🎯 Best Risk-Adjusted Strategy:")
    print(f"   Threshold: {best_sharpe['Threshold']:.1f}%")
    print(f"   Sharpe Ratio: {best_sharpe['sharpe_ratio']:.2f}")
    print(f"   Total Return: {best_sharpe['total_return_pct']:.2f}%")
    print(f"   Volatility: {best_sharpe['volatility_pct']:.2f}%")
    
    # 生成图表
    strategy.plot_threshold_comparison(results_df, output_dir)
    
    # 保存结果
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_path = os.path.join(output_dir, f'optimized_threshold_results_{current_date}.csv')
    results_df.to_csv(results_path, index=False, encoding='utf-8-sig')
    print(f"💾 Results saved: {results_path}")
    
    print(f"\n{'='*80}")
    print("    Strategy Recommendation")
    print(f"{'='*80}")
    print("🎯 Optimal Configuration:")
    print(f"   • Threshold: {best_return['Threshold']:.1f}% HSGT change")
    print(f"   • Strategy: Buy highest HSGT increase stock above threshold")
    print(f"   • Expected Return: {best_return['total_return_pct']:.1f}% total")
    print(f"   • Risk Profile: {best_return['sharpe_ratio']:.2f} Sharpe ratio")
    print(f"   • Trading Frequency: {best_return['stock_days_pct']:.1f}% of days")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
