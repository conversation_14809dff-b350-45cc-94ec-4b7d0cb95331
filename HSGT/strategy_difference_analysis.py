#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略差异分析 - 找出两个HSGT策略结果差异的根本原因

比较分析：
1. 我们的策略：当日开盘买入，收盘卖出 (-56.30%)
2. 优化策略：前一日收盘买入，次日收盘卖出 (+464.29%)
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class StrategyDifferenceAnalysis:
    """策略差异分析类"""
    
    def __init__(self):
        self.data = None
        
    def load_data(self, data_path: str):
        """加载数据"""
        try:
            self.data = pd.read_parquet(data_path)
            self.data['time_key'] = pd.to_datetime(self.data['time_key'])
            self.data = self.data[self.data['hsgt_holding_ratio'].notna()].copy()
            print(f"Data loaded: {len(self.data)} records")
            return True
        except Exception as e:
            print(f"Failed to load data: {e}")
            return False
    
    def prepare_data(self):
        """准备分析数据"""
        print("\n" + "="*60)
        print("📊 Preparing Data for Strategy Comparison")
        print("="*60)
        
        results = []
        
        for code in self.data['code'].unique():
            stock_data = self.data[self.data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            if len(stock_data) < 5:
                continue
            
            # 计算HSGT变化
            stock_data['hsgt_change_1d'] = stock_data['hsgt_holding_ratio'].diff(1)
            
            # 策略1收益：当日开盘到收盘
            stock_data['strategy1_return'] = (
                stock_data['close'] / stock_data['open'] - 1
            )
            
            # 策略2收益：前一日收盘到次日收盘
            stock_data['strategy2_return'] = (
                stock_data['close'].shift(-1) / stock_data['close'] - 1
            )
            
            # 计算价格跳空（开盘价相对前一日收盘价的变化）
            stock_data['overnight_gap'] = (
                stock_data['open'] / stock_data['close'].shift(1) - 1
            )
            
            results.append(stock_data)
        
        self.analysis_data = pd.concat(results, ignore_index=True)
        print(f"Analysis data prepared: {len(self.analysis_data)} records")
        
        return self.analysis_data
    
    def run_detailed_comparison(self, threshold=0.5):
        """运行详细的策略比较"""
        print(f"\n" + "="*60)
        print(f"🔍 Detailed Strategy Comparison (Threshold: {threshold}%)")
        print("="*60)
        
        data = self.analysis_data.copy()
        data = data.sort_values(['time_key', 'code'])
        
        # 获取交易日期
        trading_dates = sorted(data['time_key'].unique())[1:-1]
        
        strategy1_trades = []  # 当日开盘买入，收盘卖出
        strategy2_trades = []  # 前一日收盘买入，次日收盘卖出
        
        for current_date in trading_dates:
            current_data = data[data['time_key'] == current_date]
            
            # 过滤有效股票
            valid_stocks = current_data[
                (current_data['hsgt_change_1d'].notna()) &
                (current_data['hsgt_change_1d'] >= threshold) &
                (current_data['open'].notna()) &
                (current_data['close'].notna()) &
                (current_data['strategy1_return'].notna()) &
                (current_data['strategy2_return'].notna())
            ].copy()
            
            if valid_stocks.empty:
                continue
            
            # 选择HSGT增加最大的股票
            best_stock = valid_stocks.loc[valid_stocks['hsgt_change_1d'].idxmax()]
            
            # 记录策略1交易
            strategy1_trades.append({
                'date': current_date,
                'code': best_stock['code'],
                'name': best_stock.get('name', ''),
                'hsgt_change': best_stock['hsgt_change_1d'],
                'return': best_stock['strategy1_return'],
                'entry_price': best_stock['open'],
                'exit_price': best_stock['close'],
                'overnight_gap': best_stock['overnight_gap']
            })
            
            # 记录策略2交易
            strategy2_trades.append({
                'date': current_date,
                'code': best_stock['code'],
                'name': best_stock.get('name', ''),
                'hsgt_change': best_stock['hsgt_change_1d'],
                'return': best_stock['strategy2_return'],
                'entry_price': best_stock['close'],
                'exit_price': best_stock['close'] * (1 + best_stock['strategy2_return']),
                'overnight_gap': np.nan  # 不适用
            })
        
        # 转换为DataFrame
        s1_df = pd.DataFrame(strategy1_trades)
        s2_df = pd.DataFrame(strategy2_trades)
        
        # 计算累积收益
        s1_df['cumulative_return'] = (1 + s1_df['return']).cumprod()
        s2_df['cumulative_return'] = (1 + s2_df['return']).cumprod()
        
        # 打印比较结果
        print(f"\n📊 Strategy Performance Comparison:")
        print(f"{'Metric':<25} {'Strategy 1 (Intraday)':<20} {'Strategy 2 (Next Day)':<20}")
        print("-" * 70)
        
        metrics = [
            ('Total Trades', len(s1_df), len(s2_df)),
            ('Average Return (%)', s1_df['return'].mean()*100, s2_df['return'].mean()*100),
            ('Median Return (%)', s1_df['return'].median()*100, s2_df['return'].median()*100),
            ('Win Rate (%)', (s1_df['return'] > 0).mean()*100, (s2_df['return'] > 0).mean()*100),
            ('Best Trade (%)', s1_df['return'].max()*100, s2_df['return'].max()*100),
            ('Worst Trade (%)', s1_df['return'].min()*100, s2_df['return'].min()*100),
            ('Volatility (%)', s1_df['return'].std()*100, s2_df['return'].std()*100),
            ('Cumulative Return (%)', (s1_df['cumulative_return'].iloc[-1]-1)*100, (s2_df['cumulative_return'].iloc[-1]-1)*100)
        ]
        
        for metric, val1, val2 in metrics:
            print(f"{metric:<25} {val1:<20.2f} {val2:<20.2f}")
        
        # 分析隔夜跳空的影响
        print(f"\n📈 Overnight Gap Analysis (Strategy 1):")
        overnight_gaps = s1_df['overnight_gap'].dropna()
        print(f"Average Overnight Gap: {overnight_gaps.mean()*100:.2f}%")
        print(f"Positive Gaps: {(overnight_gaps > 0).mean()*100:.1f}%")
        print(f"Negative Gaps: {(overnight_gaps < 0).mean()*100:.1f}%")
        
        # 保存结果
        self.strategy1_results = s1_df
        self.strategy2_results = s2_df
        
        return s1_df, s2_df
    
    def analyze_hsgt_effectiveness(self):
        """分析HSGT变化的有效性"""
        print(f"\n" + "="*60)
        print("🔍 HSGT Change Effectiveness Analysis")
        print("="*60)
        
        # 按HSGT变化大小分组分析
        data = self.analysis_data[
            (self.analysis_data['hsgt_change_1d'].notna()) &
            (self.analysis_data['strategy1_return'].notna()) &
            (self.analysis_data['strategy2_return'].notna())
        ].copy()
        
        # 创建HSGT变化分组
        data['hsgt_group'] = pd.cut(
            data['hsgt_change_1d'], 
            bins=[0, 0.5, 1.0, 2.0, 5.0, float('inf')], 
            labels=['0-0.5%', '0.5-1%', '1-2%', '2-5%', '>5%']
        )
        
        print(f"\n📊 Returns by HSGT Change Groups:")
        print(f"{'HSGT Group':<12} {'Count':<8} {'Strategy1 Avg':<15} {'Strategy2 Avg':<15} {'Difference':<12}")
        print("-" * 70)
        
        for group in data['hsgt_group'].cat.categories:
            group_data = data[data['hsgt_group'] == group]
            if len(group_data) > 0:
                s1_avg = group_data['strategy1_return'].mean() * 100
                s2_avg = group_data['strategy2_return'].mean() * 100
                diff = s2_avg - s1_avg
                print(f"{group:<12} {len(group_data):<8} {s1_avg:<15.2f} {s2_avg:<15.2f} {diff:<12.2f}")
        
        return data
    
    def create_visualization(self, output_dir):
        """创建可视化图表"""
        os.makedirs(output_dir, exist_ok=True)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Strategy Difference Analysis', fontsize=16, fontweight='bold')
        
        # 1. 累积收益对比
        ax1 = axes[0, 0]
        ax1.plot(self.strategy1_results['cumulative_return'].values, 
                label='Strategy 1 (Intraday)', linewidth=2, color='blue')
        ax1.plot(self.strategy2_results['cumulative_return'].values, 
                label='Strategy 2 (Next Day)', linewidth=2, color='red')
        ax1.set_title('Cumulative Returns Comparison')
        ax1.set_xlabel('Trade Number')
        ax1.set_ylabel('Cumulative Return')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 收益分布对比
        ax2 = axes[0, 1]
        ax2.hist(self.strategy1_results['return']*100, bins=30, alpha=0.5, 
                label='Strategy 1', color='blue', density=True)
        ax2.hist(self.strategy2_results['return']*100, bins=30, alpha=0.5, 
                label='Strategy 2', color='red', density=True)
        ax2.set_title('Return Distribution')
        ax2.set_xlabel('Return (%)')
        ax2.set_ylabel('Density')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 隔夜跳空分析
        ax3 = axes[0, 2]
        overnight_gaps = self.strategy1_results['overnight_gap'].dropna() * 100
        ax3.hist(overnight_gaps, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax3.set_title('Overnight Gap Distribution')
        ax3.set_xlabel('Overnight Gap (%)')
        ax3.set_ylabel('Frequency')
        ax3.axvline(overnight_gaps.mean(), color='red', linestyle='--', 
                   label=f'Mean: {overnight_gaps.mean():.2f}%')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. HSGT变化 vs 收益散点图
        ax4 = axes[1, 0]
        ax4.scatter(self.strategy1_results['hsgt_change'], 
                   self.strategy1_results['return']*100, 
                   alpha=0.6, color='blue', label='Strategy 1')
        ax4.set_title('HSGT Change vs Strategy 1 Returns')
        ax4.set_xlabel('HSGT Change (%)')
        ax4.set_ylabel('Return (%)')
        ax4.grid(True, alpha=0.3)
        
        # 5. HSGT变化 vs 收益散点图 (策略2)
        ax5 = axes[1, 1]
        ax5.scatter(self.strategy2_results['hsgt_change'], 
                   self.strategy2_results['return']*100, 
                   alpha=0.6, color='red', label='Strategy 2')
        ax5.set_title('HSGT Change vs Strategy 2 Returns')
        ax5.set_xlabel('HSGT Change (%)')
        ax5.set_ylabel('Return (%)')
        ax5.grid(True, alpha=0.3)
        
        # 6. 月度表现对比
        ax6 = axes[1, 2]
        s1_monthly = self.strategy1_results.set_index('date').resample('M')['return'].sum()
        s2_monthly = self.strategy2_results.set_index('date').resample('M')['return'].sum()
        
        months = range(len(s1_monthly))
        width = 0.35
        ax6.bar([m - width/2 for m in months], s1_monthly*100, width, 
               label='Strategy 1', alpha=0.7, color='blue')
        ax6.bar([m + width/2 for m in months], s2_monthly*100, width, 
               label='Strategy 2', alpha=0.7, color='red')
        ax6.set_title('Monthly Returns Comparison')
        ax6.set_xlabel('Month')
        ax6.set_ylabel('Monthly Return (%)')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(output_dir, 'strategy_difference_analysis.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"\n📊 Strategy difference analysis chart saved: {chart_path}")
        
        plt.show()

def main():
    """主函数"""
    print("="*80)
    print("    Strategy Difference Analysis")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    output_dir = os.path.join(script_dir, 'strategy_difference_results')
    
    # 创建分析实例
    analyzer = StrategyDifferenceAnalysis()
    
    # 加载数据
    if not analyzer.load_data(data_path):
        return
    
    # 准备数据
    analyzer.prepare_data()
    
    # 运行详细比较
    s1_df, s2_df = analyzer.run_detailed_comparison(threshold=0.5)
    
    # 分析HSGT有效性
    analyzer.analyze_hsgt_effectiveness()
    
    # 创建可视化
    analyzer.create_visualization(output_dir)
    
    print(f"\n{'='*80}")
    print("    Key Findings")
    print(f"{'='*80}")
    print("🔍 The main difference between strategies:")
    print("   Strategy 1: Intraday trading (open to close)")
    print("   Strategy 2: Overnight holding (close to next close)")
    print("\n💡 This suggests that HSGT changes may have")
    print("   delayed effects that show up the next day,")
    print("   rather than immediate intraday effects.")

if __name__ == "__main__":
    main()
