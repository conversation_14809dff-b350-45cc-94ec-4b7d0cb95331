#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析每日交易策略结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 设置图表样式
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def analyze_strategy_results():
    """分析策略结果"""
    
    # 读取最新的结果文件
    results_dir = 'enhanced_backtest_results'
    
    # 获取最新的文件
    files = os.listdir(results_dir)
    trade_files = [f for f in files if f.startswith('enhanced_trades_')]
    portfolio_files = [f for f in files if f.startswith('enhanced_portfolio_')]
    
    if not trade_files or not portfolio_files:
        print("未找到结果文件")
        return
    
    # 选择最新的文件
    latest_trade_file = sorted(trade_files)[-1]
    latest_portfolio_file = sorted(portfolio_files)[-1]
    
    print(f"分析文件: {latest_trade_file}")
    print(f"分析文件: {latest_portfolio_file}")
    
    # 读取数据
    trades_df = pd.read_csv(os.path.join(results_dir, latest_trade_file))
    portfolio_df = pd.read_csv(os.path.join(results_dir, latest_portfolio_file))
    
    # 转换日期
    trades_df['date'] = pd.to_datetime(trades_df['date'])
    portfolio_df['date'] = pd.to_datetime(portfolio_df['date'])
    
    print(f"\n{'='*60}")
    print("📊 策略表现分析")
    print(f"{'='*60}")
    
    # 基本统计
    total_trading_days = len(trades_df)
    total_days = len(portfolio_df)
    trading_frequency = total_trading_days / total_days * 100
    
    print(f"总交易日数: {total_trading_days}")
    print(f"总回测天数: {total_days}")
    print(f"交易频率: {trading_frequency:.1f}%")
    
    # 收益分析
    final_value = portfolio_df['portfolio_value'].iloc[-1]
    initial_value = portfolio_df['portfolio_value'].iloc[0]
    total_return = (final_value / initial_value - 1) * 100
    
    print(f"\n💰 收益分析:")
    print(f"初始资金: {initial_value:,.0f}")
    print(f"最终资金: {final_value:,.0f}")
    print(f"总收益率: {total_return:.2f}%")
    
    # 交易分析
    winning_trades = trades_df[trades_df['daily_return_pct'] > 0]
    losing_trades = trades_df[trades_df['daily_return_pct'] < 0]
    
    win_rate = len(winning_trades) / len(trades_df) * 100
    avg_win = winning_trades['daily_return_pct'].mean()
    avg_loss = losing_trades['daily_return_pct'].mean()
    
    print(f"\n📈 交易分析:")
    print(f"胜率: {win_rate:.1f}%")
    print(f"平均盈利: {avg_win:.2f}%")
    print(f"平均亏损: {avg_loss:.2f}%")
    print(f"盈亏比: {abs(avg_win/avg_loss):.2f}" if avg_loss != 0 else "N/A")
    
    # HSGT阈值分析
    hsgt_changes = trades_df['hsgt_change_1d']
    print(f"\n🌏 HSGT变化分析:")
    print(f"HSGT变化范围: {hsgt_changes.min():.2f}% ~ {hsgt_changes.max():.2f}%")
    print(f"平均HSGT变化: {hsgt_changes.mean():.2f}%")
    print(f"HSGT变化中位数: {hsgt_changes.median():.2f}%")
    
    # 按HSGT变化分组分析收益
    trades_df['hsgt_group'] = pd.cut(trades_df['hsgt_change_1d'], 
                                   bins=[0.5, 1.0, 2.0, 5.0, float('inf')], 
                                   labels=['0.5-1%', '1-2%', '2-5%', '>5%'])
    
    print(f"\n📊 按HSGT变化分组的收益:")
    for group in trades_df['hsgt_group'].cat.categories:
        group_data = trades_df[trades_df['hsgt_group'] == group]
        if len(group_data) > 0:
            avg_return = group_data['daily_return_pct'].mean()
            win_rate_group = (group_data['daily_return_pct'] > 0).mean() * 100
            print(f"  {group}: 平均收益 {avg_return:.2f}%, 胜率 {win_rate_group:.1f}%, 交易次数 {len(group_data)}")
    
    # 月度表现
    portfolio_df['year_month'] = portfolio_df['date'].dt.to_period('M')
    monthly_returns = portfolio_df.groupby('year_month').agg({
        'portfolio_value': ['first', 'last']
    }).round(2)
    monthly_returns.columns = ['start_value', 'end_value']
    monthly_returns['monthly_return'] = (monthly_returns['end_value'] / monthly_returns['start_value'] - 1) * 100
    
    print(f"\n📅 月度表现 (最近12个月):")
    recent_months = monthly_returns.tail(12)
    for period, row in recent_months.iterrows():
        print(f"  {period}: {row['monthly_return']:+.2f}%")
    
    # 创建图表
    create_performance_charts(portfolio_df, trades_df)
    
    return trades_df, portfolio_df

def create_performance_charts(portfolio_df, trades_df):
    """创建表现图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('HSGT每日交易策略表现分析', fontsize=16)
    
    # 1. 组合价值走势
    axes[0, 0].plot(portfolio_df['date'], portfolio_df['portfolio_value'], linewidth=2)
    axes[0, 0].set_title('组合价值走势')
    axes[0, 0].set_ylabel('组合价值')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 2. 日收益率分布
    daily_returns = portfolio_df['daily_return'].dropna()
    axes[0, 1].hist(daily_returns, bins=50, alpha=0.7, edgecolor='black')
    axes[0, 1].set_title('日收益率分布')
    axes[0, 1].set_xlabel('日收益率 (%)')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].axvline(daily_returns.mean(), color='red', linestyle='--', label=f'均值: {daily_returns.mean():.2f}%')
    axes[0, 1].legend()
    
    # 3. HSGT变化 vs 收益率散点图
    axes[1, 0].scatter(trades_df['hsgt_change_1d'], trades_df['daily_return_pct'], alpha=0.6)
    axes[1, 0].set_title('HSGT变化 vs 当日收益率')
    axes[1, 0].set_xlabel('HSGT变化 (%)')
    axes[1, 0].set_ylabel('当日收益率 (%)')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].axhline(0, color='red', linestyle='--', alpha=0.5)
    
    # 4. 交易频率
    portfolio_df['has_trade'] = portfolio_df['num_trades'] > 0
    trade_freq = portfolio_df.groupby(portfolio_df['date'].dt.to_period('M'))['has_trade'].mean() * 100
    
    axes[1, 1].bar(range(len(trade_freq)), trade_freq.values)
    axes[1, 1].set_title('月度交易频率')
    axes[1, 1].set_ylabel('交易频率 (%)')
    axes[1, 1].set_xticks(range(0, len(trade_freq), 3))
    axes[1, 1].set_xticklabels([str(trade_freq.index[i]) for i in range(0, len(trade_freq), 3)], rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = 'enhanced_backtest_results/daily_strategy_analysis.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"\n📊 图表已保存: {chart_path}")
    
    plt.show()

if __name__ == "__main__":
    print("="*80)
    print("           HSGT每日交易策略结果分析")
    print("="*80)
    
    analyze_strategy_results()
