#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通比例变化与股价预测能力的全面分析

分析港股通持股比例变化对未来股价变动的预测作用，
并找出不同策略结果差异的根本原因
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class ComprehensiveHSGTAnalysis:
    """港股通全面分析类"""
    
    def __init__(self):
        self.data = None
        self.analysis_results = {}
        
    def load_data(self, data_path: str):
        """加载数据"""
        try:
            self.data = pd.read_parquet(data_path)
            self.data['time_key'] = pd.to_datetime(self.data['time_key'])
            self.data = self.data[self.data['hsgt_holding_ratio'].notna()].copy()
            print(f"Data loaded: {len(self.data)} records with HSGT data")
            print(f"Date range: {self.data['time_key'].min()} to {self.data['time_key'].max()}")
            print(f"Unique stocks: {self.data['code'].nunique()}")
            return True
        except Exception as e:
            print(f"Failed to load data: {e}")
            return False
    
    def prepare_analysis_data(self):
        """准备分析数据"""
        print("\n" + "="*60)
        print("📊 Preparing Analysis Data")
        print("="*60)
        
        results = []
        
        for code in self.data['code'].unique():
            stock_data = self.data[self.data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            if len(stock_data) < 10:  # 至少需要10天数据
                continue
            
            # 计算HSGT变化
            stock_data['hsgt_change_1d'] = stock_data['hsgt_holding_ratio'].diff(1)
            stock_data['hsgt_change_3d'] = stock_data['hsgt_holding_ratio'].diff(3)
            stock_data['hsgt_change_5d'] = stock_data['hsgt_holding_ratio'].diff(5)
            
            # 计算未来收益率（不同时间窗口）
            for days in [1, 2, 3, 5, 10]:
                # 方法1：使用收盘价计算未来收益
                stock_data[f'future_return_{days}d_close'] = (
                    stock_data['close'].shift(-days) / stock_data['close'] - 1
                )
                
                # 方法2：使用开盘价到未来收盘价（模拟实际交易）
                if 'open' in stock_data.columns:
                    stock_data[f'future_return_{days}d_open_to_close'] = (
                        stock_data['close'].shift(-days) / stock_data['open'].shift(-1) - 1
                    )
                
                # 方法3：当日开盘到收盘收益率
                if days == 1 and 'open' in stock_data.columns:
                    stock_data['intraday_return'] = (
                        stock_data['close'] / stock_data['open'] - 1
                    )
            
            # 计算波动率
            stock_data['volatility_20d'] = stock_data['close'].pct_change().rolling(20).std() * np.sqrt(252)
            
            results.append(stock_data)
        
        self.analysis_data = pd.concat(results, ignore_index=True)
        print(f"Analysis data prepared: {len(self.analysis_data)} records")
        
        return self.analysis_data
    
    def correlation_analysis(self):
        """相关性分析"""
        print("\n" + "="*60)
        print("🔍 HSGT Change vs Future Returns Correlation Analysis")
        print("="*60)
        
        # 定义要分析的变量组合
        hsgt_vars = ['hsgt_change_1d', 'hsgt_change_3d', 'hsgt_change_5d']
        return_vars = [
            'future_return_1d_close', 'future_return_2d_close', 'future_return_3d_close',
            'future_return_5d_close', 'future_return_10d_close',
            'future_return_1d_open_to_close', 'intraday_return'
        ]
        
        correlations = []
        
        for hsgt_var in hsgt_vars:
            for return_var in return_vars:
                # 过滤有效数据
                valid_data = self.analysis_data[
                    (self.analysis_data[hsgt_var].notna()) &
                    (self.analysis_data[return_var].notna())
                ].copy()
                
                if len(valid_data) < 100:  # 至少需要100个观测值
                    continue
                
                # 计算相关系数
                corr_coef = np.corrcoef(valid_data[hsgt_var], valid_data[return_var])[0, 1]

                # 简单的显著性检验（基于样本量）
                n = len(valid_data)
                t_stat = corr_coef * np.sqrt((n - 2) / (1 - corr_coef**2)) if abs(corr_coef) < 0.999 else 10
                p_value = 2 * (1 - 0.95) if abs(t_stat) > 1.96 else 0.1  # 简化的p值估计
                
                # 计算分组统计
                # 将HSGT变化分为5个分位数组
                valid_data['hsgt_quintile'] = pd.qcut(valid_data[hsgt_var], 5, labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'])
                quintile_stats = valid_data.groupby('hsgt_quintile')[return_var].agg(['mean', 'std', 'count'])
                
                correlations.append({
                    'hsgt_variable': hsgt_var,
                    'return_variable': return_var,
                    'correlation': corr_coef,
                    'p_value': p_value,
                    'sample_size': len(valid_data),
                    'significant': p_value < 0.05,
                    'q1_mean_return': quintile_stats.loc['Q1', 'mean'],
                    'q5_mean_return': quintile_stats.loc['Q5', 'mean'],
                    'q5_minus_q1': quintile_stats.loc['Q5', 'mean'] - quintile_stats.loc['Q1', 'mean']
                })
        
        self.correlation_results = pd.DataFrame(correlations)
        
        # 打印关键结果
        print("\n📈 Key Correlation Results:")
        print("-" * 80)
        significant_corrs = self.correlation_results[self.correlation_results['significant']].copy()
        significant_corrs = significant_corrs.sort_values('correlation', key=abs, ascending=False)
        
        for _, row in significant_corrs.head(10).iterrows():
            print(f"{row['hsgt_variable']:15} vs {row['return_variable']:25} | "
                  f"Corr: {row['correlation']:6.3f} | "
                  f"P-val: {row['p_value']:6.3f} | "
                  f"Q5-Q1: {row['q5_minus_q1']*100:6.2f}%")
        
        return self.correlation_results
    
    def strategy_comparison_analysis(self):
        """策略比较分析 - 找出差异原因"""
        print("\n" + "="*60)
        print("🔍 Strategy Comparison Analysis")
        print("="*60)
        
        # 模拟两种不同的策略逻辑
        data = self.analysis_data.copy()
        data = data.sort_values(['time_key', 'code'])
        
        # 策略1：当日开盘买入，收盘卖出（我们的策略）
        strategy1_results = []
        
        # 策略2：前一日收盘买入，次日收盘卖出（优化策略）
        strategy2_results = []
        
        trading_dates = sorted(data['time_key'].unique())[1:-1]  # 排除首尾日期
        
        for current_date in trading_dates[:100]:  # 先分析前100个交易日
            current_data = data[data['time_key'] == current_date]
            
            # 过滤有效股票
            valid_stocks = current_data[
                (current_data['hsgt_change_1d'].notna()) &
                (current_data['hsgt_change_1d'] >= 0.5) &  # 0.5%阈值
                (current_data['open'].notna()) &
                (current_data['close'].notna()) &
                (current_data['future_return_1d_close'].notna())
            ].copy()
            
            if valid_stocks.empty:
                continue
            
            # 选择HSGT增加最大的股票
            best_stock = valid_stocks.loc[valid_stocks['hsgt_change_1d'].idxmax()]
            
            # 策略1：当日开盘买入，收盘卖出
            if pd.notna(best_stock['intraday_return']):
                strategy1_results.append({
                    'date': current_date,
                    'code': best_stock['code'],
                    'hsgt_change': best_stock['hsgt_change_1d'],
                    'return': best_stock['intraday_return'],
                    'entry_price': best_stock['open'],
                    'exit_price': best_stock['close']
                })
            
            # 策略2：前一日收盘买入，次日收盘卖出
            if pd.notna(best_stock['future_return_1d_close']):
                strategy2_results.append({
                    'date': current_date,
                    'code': best_stock['code'],
                    'hsgt_change': best_stock['hsgt_change_1d'],
                    'return': best_stock['future_return_1d_close'],
                    'entry_price': best_stock['close'],
                    'exit_price': best_stock['close'] * (1 + best_stock['future_return_1d_close'])
                })
        
        # 比较结果
        if strategy1_results and strategy2_results:
            s1_df = pd.DataFrame(strategy1_results)
            s2_df = pd.DataFrame(strategy2_results)
            
            print(f"\n📊 Strategy Comparison (First 100 trading days):")
            print(f"Strategy 1 (Intraday): {len(s1_df)} trades")
            print(f"  Average Return: {s1_df['return'].mean()*100:.2f}%")
            print(f"  Win Rate: {(s1_df['return'] > 0).mean()*100:.1f}%")
            print(f"  Cumulative Return: {(1 + s1_df['return']).prod()-1:.2%}")
            
            print(f"\nStrategy 2 (Next Day): {len(s2_df)} trades")
            print(f"  Average Return: {s2_df['return'].mean()*100:.2f}%")
            print(f"  Win Rate: {(s2_df['return'] > 0).mean()*100:.1f}%")
            print(f"  Cumulative Return: {(1 + s2_df['return']).prod()-1:.2%}")
            
            self.strategy_comparison = {
                'strategy1': s1_df,
                'strategy2': s2_df
            }
        
        return self.strategy_comparison
    
    def generate_comprehensive_report(self, output_dir):
        """生成综合报告"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('Comprehensive HSGT Analysis Report', fontsize=16, fontweight='bold')
        
        # 1. 相关性热力图
        ax1 = axes[0, 0]
        corr_matrix = self.correlation_results.pivot(
            index='hsgt_variable', 
            columns='return_variable', 
            values='correlation'
        )
        sns.heatmap(corr_matrix, annot=True, cmap='RdBu_r', center=0, ax=ax1, fmt='.3f')
        ax1.set_title('HSGT Change vs Future Returns Correlation')
        
        # 2. 显著性分析
        ax2 = axes[0, 1]
        significant_corrs = self.correlation_results[self.correlation_results['significant']]
        ax2.scatter(significant_corrs['correlation'], significant_corrs['q5_minus_q1'], alpha=0.7)
        ax2.set_xlabel('Correlation Coefficient')
        ax2.set_ylabel('Q5 - Q1 Return Difference')
        ax2.set_title('Significant Correlations')
        ax2.grid(True, alpha=0.3)
        
        # 3. HSGT变化分布
        ax3 = axes[0, 2]
        hsgt_changes = self.analysis_data['hsgt_change_1d'].dropna()
        ax3.hist(hsgt_changes, bins=50, alpha=0.7, edgecolor='black')
        ax3.set_xlabel('HSGT 1-Day Change (%)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Distribution of HSGT Changes')
        ax3.axvline(0.5, color='red', linestyle='--', label='0.5% Threshold')
        ax3.legend()
        
        # 4. 策略比较（如果有数据）
        if hasattr(self, 'strategy_comparison'):
            ax4 = axes[1, 0]
            s1_returns = self.strategy_comparison['strategy1']['return']
            s2_returns = self.strategy_comparison['strategy2']['return']
            
            ax4.hist(s1_returns, bins=20, alpha=0.5, label='Strategy 1 (Intraday)', color='blue')
            ax4.hist(s2_returns, bins=20, alpha=0.5, label='Strategy 2 (Next Day)', color='red')
            ax4.set_xlabel('Return (%)')
            ax4.set_ylabel('Frequency')
            ax4.set_title('Strategy Returns Distribution')
            ax4.legend()
        
        # 5. 累积收益对比
        if hasattr(self, 'strategy_comparison'):
            ax5 = axes[1, 1]
            s1_cumret = (1 + self.strategy_comparison['strategy1']['return']).cumprod()
            s2_cumret = (1 + self.strategy_comparison['strategy2']['return']).cumprod()
            
            ax5.plot(s1_cumret.values, label='Strategy 1 (Intraday)', linewidth=2)
            ax5.plot(s2_cumret.values, label='Strategy 2 (Next Day)', linewidth=2)
            ax5.set_xlabel('Trade Number')
            ax5.set_ylabel('Cumulative Return')
            ax5.set_title('Cumulative Returns Comparison')
            ax5.legend()
            ax5.grid(True, alpha=0.3)
        
        # 6. 关键统计摘要
        ax6 = axes[1, 2]
        ax6.axis('off')
        
        # 创建统计摘要文本
        summary_text = "Key Findings:\n\n"
        
        if hasattr(self, 'correlation_results'):
            best_corr = self.correlation_results.loc[
                self.correlation_results['correlation'].abs().idxmax()
            ]
            summary_text += f"Strongest Correlation:\n"
            summary_text += f"{best_corr['hsgt_variable']} vs\n{best_corr['return_variable']}\n"
            summary_text += f"Corr: {best_corr['correlation']:.3f}\n\n"
        
        if hasattr(self, 'strategy_comparison'):
            s1_ret = self.strategy_comparison['strategy1']['return'].mean()
            s2_ret = self.strategy_comparison['strategy2']['return'].mean()
            summary_text += f"Strategy Performance:\n"
            summary_text += f"Intraday: {s1_ret*100:.2f}%\n"
            summary_text += f"Next Day: {s2_ret*100:.2f}%\n"
            summary_text += f"Difference: {(s2_ret-s1_ret)*100:.2f}%\n"
        
        ax6.text(0.1, 0.9, summary_text, transform=ax6.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(output_dir, 'comprehensive_hsgt_analysis.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"\n📊 Comprehensive analysis chart saved: {chart_path}")
        
        # 保存数据
        if hasattr(self, 'correlation_results'):
            corr_path = os.path.join(output_dir, 'correlation_results.csv')
            self.correlation_results.to_csv(corr_path, index=False)
            print(f"📊 Correlation results saved: {corr_path}")
        
        plt.show()

def main():
    """主函数"""
    print("="*80)
    print("    Comprehensive HSGT Predictive Power Analysis")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    output_dir = os.path.join(script_dir, 'comprehensive_analysis_results')
    
    # 创建分析实例
    analyzer = ComprehensiveHSGTAnalysis()
    
    # 加载数据
    if not analyzer.load_data(data_path):
        return
    
    # 准备分析数据
    analyzer.prepare_analysis_data()
    
    # 相关性分析
    analyzer.correlation_analysis()
    
    # 策略比较分析
    analyzer.strategy_comparison_analysis()
    
    # 生成综合报告
    analyzer.generate_comprehensive_report(output_dir)
    
    print(f"\n{'='*80}")
    print("    Analysis Complete")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
