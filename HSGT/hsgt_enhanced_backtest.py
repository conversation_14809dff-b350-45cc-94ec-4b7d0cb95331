#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSGT增强回测策略 - 参考RoC策略架构

主要改进：
1. 多股票组合策略（而非单股票）
2. 动态权重分配
3. 随机基准对比
4. 详细的性能归因分析
5. 样本内外测试
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTEnhancedStrategy:
    """HSGT增强策略 - 多股票组合版本"""
    
    def __init__(self, initial_capital: float = 1000000, max_positions: int = 10):
        self.initial_capital = initial_capital
        self.max_positions = max_positions
        self.data = None
        self.trades_log = []
        self.portfolio_history = []
        
    def load_data(self, data_path: str):
        """加载数据"""
        try:
            self.data = pd.read_parquet(data_path)
            self.data['time_key'] = pd.to_datetime(self.data['time_key'])
            self.data = self.data[self.data['hsgt_holding_ratio'].notna()].copy()
            
            # 计算HSGT变化
            self.calculate_hsgt_changes()
            
            print(f"Data loaded: {len(self.data)} records")
            print(f"Date range: {self.data['time_key'].min()} to {self.data['time_key'].max()}")
            print(f"Unique stocks: {self.data['code'].nunique()}")
            return True
        except Exception as e:
            print(f"Failed to load data: {e}")
            return False
    
    def calculate_hsgt_changes(self):
        """计算HSGT变化和未来收益"""
        results = []
        
        for code in self.data['code'].unique():
            stock_data = self.data[self.data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            if len(stock_data) < 10:  # 至少需要10天数据
                continue
            
            # 计算不同周期的HSGT变化
            stock_data['hsgt_change_1d'] = stock_data['hsgt_holding_ratio'].diff(1)
            stock_data['hsgt_change_3d'] = stock_data['hsgt_holding_ratio'].diff(3)
            stock_data['hsgt_change_5d'] = stock_data['hsgt_holding_ratio'].diff(5)
            
            # 计算未来收益率
            for days in [1, 3, 5]:
                stock_data[f'future_return_{days}d'] = (
                    stock_data['close'].shift(-days) / stock_data['close'] - 1
                )
            
            # 计算波动率
            stock_data['volatility_20d'] = stock_data['close'].pct_change().rolling(20).std() * np.sqrt(252)
            
            results.append(stock_data)
        
        self.data = pd.concat(results, ignore_index=True)
        print(f"HSGT changes calculated for {len(results)} stocks")
    
    def generate_trading_signals(self, date, hsgt_threshold=0.5):
        """生成交易信号 - 每日交易港股通增加最大的股票"""
        current_data = self.data[self.data['time_key'] == date].copy()

        # 过滤条件：港股通前一天增加的股票
        valid_stocks = current_data[
            (current_data['hsgt_change_1d'].notna()) &
            (current_data['open'].notna()) &  # 确保有开盘价
            (current_data['close'].notna()) &  # 确保有收盘价
            (current_data['hsgt_change_1d'] >= hsgt_threshold)  # HSGT增长阈值0.5%
        ].copy()

        if valid_stocks.empty:
            return []

        # 按HSGT变化排序，选择增加最大的那一只股票
        valid_stocks = valid_stocks.sort_values('hsgt_change_1d', ascending=False)
        top_stock = valid_stocks.iloc[0]  # 只选择HSGT增加最大的一只股票

        signals = [{
            'date': date,
            'code': top_stock['code'],
            'name': top_stock.get('name', ''),
            'entry_price': top_stock['open'],  # 以开盘价买入
            'exit_price': top_stock['close'],   # 以收盘价卖出
            'weight': 1.0,  # 全仓买入这一只股票
            'hsgt_change_1d': top_stock['hsgt_change_1d'],
            'hsgt_change_3d': top_stock['hsgt_change_3d'],
            'volatility': top_stock['volatility_20d'],
            'daily_return': (top_stock['close'] / top_stock['open'] - 1) * 100  # 当日收益率
        }]

        return signals
    
    def run_portfolio_backtest(self, start_date=None, end_date=None,
                              hsgt_threshold=0.5):
        """运行组合回测 - 修改为每日交易策略"""
        print(f"\n{'='*60}")
        print("🚀 Running HSGT Daily Trading Strategy Backtest")
        print(f"{'='*60}")

        if start_date is None:
            start_date = self.data['time_key'].min()
        if end_date is None:
            end_date = self.data['time_key'].max()

        print(f"Backtest period: {start_date} to {end_date}")
        print(f"HSGT threshold: {hsgt_threshold}%")
        print(f"Strategy: Daily trading single stock (highest HSGT increase)")
        print(f"Logic: Buy at open, sell at close, full position")

        # 获取交易日期
        trading_dates = sorted(self.data['time_key'].unique())
        trading_dates = [d for d in trading_dates if start_date <= d <= end_date]

        # 初始化
        portfolio_value = self.initial_capital
        self.trades_log = []
        self.portfolio_history = []

        for i, current_date in enumerate(trading_dates):
            # 生成当日交易信号
            signals = self.generate_trading_signals(current_date, hsgt_threshold)

            daily_return = 0.0
            daily_trades = 0

            if signals:
                # 执行当日交易（开盘买入，收盘卖出）
                for signal in signals:
                    # 计算投资金额
                    position_value = portfolio_value * signal['weight']
                    shares = position_value / signal['entry_price']

                    # 计算当日收益
                    trade_return = (signal['exit_price'] / signal['entry_price'] - 1)
                    daily_return += trade_return * signal['weight']
                    daily_trades += 1

                    # 记录交易
                    self.trades_log.append({
                        'date': current_date,
                        'code': signal['code'],
                        'name': signal['name'],
                        'action': 'BUY_SELL',  # 当日买卖
                        'entry_price': signal['entry_price'],
                        'exit_price': signal['exit_price'],
                        'shares': shares,
                        'position_value': position_value,
                        'weight': signal['weight'],
                        'hsgt_change_1d': signal['hsgt_change_1d'],
                        'daily_return_pct': trade_return * 100,
                        'pnl': position_value * trade_return
                    })

            # 更新组合价值
            portfolio_value *= (1 + daily_return)

            # 记录组合历史
            self.portfolio_history.append({
                'date': current_date,
                'portfolio_value': portfolio_value,
                'daily_return': daily_return * 100,
                'num_trades': daily_trades,
                'trading_signal_count': len(signals) if signals else 0
            })

        print(f"Backtest completed. Total trading days: {len([t for t in self.trades_log if t['action'] == 'BUY_SELL'])}")
        return pd.DataFrame(self.trades_log), pd.DataFrame(self.portfolio_history)
    
    # 注意：由于改为每日交易策略，以下方法不再需要
    # 每日开盘买入，收盘卖出，不需要持仓管理
    
    def run_random_benchmark(self, num_iterations=50, **backtest_params):
        """运行随机基准测试 - 适配每日交易策略"""
        print(f"\n{'='*60}")
        print("🎲 Random Benchmark Test (Daily Trading)")
        print(f"{'='*60}")

        random_results = []

        for i in range(num_iterations):
            # 创建随机策略实例
            random_strategy = HSGTRandomStrategy(
                initial_capital=self.initial_capital,
                max_positions=self.max_positions
            )
            random_strategy.data = self.data.copy()

            # 运行随机回测（每日交易版本）
            _, portfolio_df = random_strategy.run_random_daily_backtest(seed=i, **backtest_params)

            if not portfolio_df.empty:
                final_value = portfolio_df['portfolio_value'].iloc[-1]
                total_return = (final_value / self.initial_capital - 1) * 100
                random_results.append(total_return)

        if random_results:
            random_mean = np.mean(random_results)
            random_std = np.std(random_results)

            # 策略表现
            if self.portfolio_history:
                strategy_return = (self.portfolio_history[-1]['portfolio_value'] / self.initial_capital - 1) * 100

                print(f"Strategy Return: {strategy_return:.2f}%")
                print(f"Random Benchmark: {random_mean:.2f}% ± {random_std:.2f}%")

                # 计算统计显著性
                outperform_count = sum(1 for r in random_results if strategy_return > r)
                significance = outperform_count / len(random_results) * 100

                print(f"Strategy outperforms random: {outperform_count}/{len(random_results)} times ({significance:.1f}%)")

        return random_results

class HSGTRandomStrategy:
    """随机策略基准"""

    def __init__(self, initial_capital, max_positions):
        self.initial_capital = initial_capital
        self.max_positions = max_positions
        self.data = None

    def run_random_daily_backtest(self, seed=42, start_date=None, end_date=None):
        """运行随机每日交易回测"""
        np.random.seed(seed)

        if start_date is None:
            start_date = self.data['time_key'].min()
        if end_date is None:
            end_date = self.data['time_key'].max()

        trading_dates = sorted(self.data['time_key'].unique())
        trading_dates = [d for d in trading_dates if start_date <= d <= end_date]

        portfolio_value = self.initial_capital
        portfolio_history = []

        for current_date in trading_dates:
            # 随机选择股票进行当日交易
            current_data = self.data[
                (self.data['time_key'] == current_date) &
                (self.data['open'].notna()) &
                (self.data['close'].notna())
            ]

            daily_return = 0.0

            if len(current_data) >= 1:
                # 随机选择一只股票（与策略保持一致）
                selected_stock = current_data.sample(n=1).iloc[0]

                # 计算当日收益（开盘买入，收盘卖出）
                if selected_stock['open'] > 0:  # 避免除零错误
                    daily_return = (selected_stock['close'] / selected_stock['open'] - 1)

            # 更新组合价值
            portfolio_value *= (1 + daily_return)

            portfolio_history.append({
                'date': current_date,
                'portfolio_value': portfolio_value,
                'daily_return': daily_return * 100
            })

        return [], pd.DataFrame(portfolio_history)

def main():
    """主函数"""
    print("="*80)
    print("    HSGT Enhanced Portfolio Strategy Backtest")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    output_dir = os.path.join(script_dir, 'enhanced_backtest_results')
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建策略实例
    strategy = HSGTEnhancedStrategy(initial_capital=1000000, max_positions=1)
    
    # 加载数据
    if not strategy.load_data(data_path):
        return
    
    # 运行回测
    trades_df, portfolio_df = strategy.run_portfolio_backtest(
        hsgt_threshold=0.5  # 设置阈值为0.5%
    )
    
    # 计算性能指标
    if not portfolio_df.empty:
        final_value = portfolio_df['portfolio_value'].iloc[-1]
        total_return = (final_value / strategy.initial_capital - 1) * 100
        
        daily_returns = portfolio_df['daily_return'].values / 100
        volatility = np.std(daily_returns) * np.sqrt(252) * 100
        sharpe_ratio = (total_return - 3) / volatility if volatility > 0 else 0
        
        print(f"\n{'='*60}")
        print("📊 Strategy Performance")
        print(f"{'='*60}")
        print(f"Total Return: {total_return:.2f}%")
        print(f"Volatility: {volatility:.2f}%")
        print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"Total Trades: {len(trades_df)}")
    
    # 运行随机基准测试
    strategy.run_random_benchmark(num_iterations=30)
    
    # 保存结果
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    if not trades_df.empty:
        trades_path = os.path.join(output_dir, f'enhanced_trades_{current_date}.csv')
        trades_df.to_csv(trades_path, index=False, encoding='utf-8-sig')
        print(f"💾 Trades saved: {trades_path}")
    
    if not portfolio_df.empty:
        portfolio_path = os.path.join(output_dir, f'enhanced_portfolio_{current_date}.csv')
        portfolio_df.to_csv(portfolio_path, index=False, encoding='utf-8-sig')
        print(f"💾 Portfolio history saved: {portfolio_path}")

if __name__ == "__main__":
    main()