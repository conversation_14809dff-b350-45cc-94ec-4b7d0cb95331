# 港股通持股比例变化预测性分析

本项目用于分析港股通持股比例变化对后续N天股价变化的先行指导作用。

## 📁 项目结构

```
HSGT/
├── README.md                           # 项目说明文档
├── run_hsgt_analysis.py               # 主运行脚本（一键运行所有分析）
├── hsgt_predictive_analysis.py        # 预测性分析脚本
├── hsgt_visualization_analysis.py     # 可视化分析脚本
├── hsgt_strategy_backtest.py          # 策略回测脚本
├── analysis_results/                  # 分析结果目录
├── visualization_results/             # 可视化结果目录
└── backtest_results/                  # 回测结果目录
```

## 🎯 分析目标

1. **相关性分析**: 分析港股通持股比例变化与未来1、3、5、10、20天股价收益的相关性
2. **统计检验**: 进行显著性检验，确定相关性的统计意义
3. **可视化展示**: 生成热力图、散点图、时间序列图等可视化结果
4. **策略回测**: 基于港股通变化构建交易策略并进行历史回测

## 🚀 快速开始

### 方法一：一键运行（推荐）

```bash
cd /Users/<USER>/Cursor/Futu/HSGT
python run_hsgt_analysis.py
```

这将自动运行所有分析步骤：
1. 港股通预测性分析
2. 可视化分析
3. 策略回测

### 方法二：分步运行

```bash
# 1. 预测性分析（必须先运行）
python hsgt_predictive_analysis.py

# 2. 可视化分析
python hsgt_visualization_analysis.py

# 3. 策略回测
python hsgt_strategy_backtest.py
```

## 📊 分析内容

### 1. 预测性分析 (`hsgt_predictive_analysis.py`)

**功能**:
- 计算港股通持股比例的1天和5天变化
- 计算未来1、3、5、10、20天的股价收益率
- 进行皮尔逊相关性分析和显著性检验
- 生成交易信号

**输出文件**:
- `hsgt_correlation_summary_*.csv`: 相关性分析汇总
- `hsgt_complete_analysis_data_*.csv`: 完整分析数据
- `hsgt_trading_signals_*.csv`: 交易信号数据

**关键指标**:
- 相关系数：衡量港股通变化与未来收益的线性关系强度
- P值：统计显著性检验结果
- 样本数量：分析所用的有效数据点数

### 2. 可视化分析 (`hsgt_visualization_analysis.py`)

**功能**:
- 生成相关性热力图
- 按时间框架分析相关性强度
- 绘制最强相关性的散点图
- 展示典型股票的时间序列

**输出图表**:
- `correlation_heatmap.png`: 相关性热力图
- `correlation_analysis_by_timeframe.png`: 时间框架分析
- `scatter_analysis_top_correlations.png`: 散点图分析
- `time_series_examples.png`: 时间序列示例

### 3. 策略回测 (`hsgt_strategy_backtest.py`)

**功能**:
- 基于港股通变化构建交易策略
- 模拟历史交易并计算绩效
- 分析风险收益特征

**策略逻辑**:
- 买入信号：港股通持股比例增加幅度排名前20%
- 卖出信号：港股通持股比例减少幅度排名后20%
- 持仓期：5个交易日
- 最大持仓：10只股票

**输出文件**:
- `backtest_trades_*.csv`: 详细交易记录
- `backtest_portfolio_*.csv`: 组合价值历史
- `backtest_metrics_*.csv`: 绩效指标汇总
- `strategy_backtest_performance.png`: 绩效图表

**绩效指标**:
- 总收益率、年化收益率
- 夏普比率、最大回撤
- 胜率、平均持仓天数

## 📋 数据要求

**输入数据**: `../data/h_shares_daily.parquet`

**必需字段**:
- `code`: 股票代码
- `name`: 股票名称
- `time_key`: 交易日期
- `close`: 收盘价
- `hsgt_holding_ratio`: 港股通持股比例(%)
- `hsgt_holding_value`: 港股通持股市值（可选）

## 🔧 参数配置

### 分析参数

```python
# 在 hsgt_predictive_analysis.py 中可调整
lookback_days = 5                    # 港股通变化回看天数
forward_days_list = [1, 3, 5, 10, 20]  # 未来收益前瞻天数
min_data_points = 50                 # 每只股票最少数据点数
```

### 策略参数

```python
# 在 hsgt_strategy_backtest.py 中可调整
initial_capital = 1000000           # 初始资金
holding_days = 5                    # 持仓天数
max_positions = 10                  # 最大持仓数量
```

## 📈 结果解读

### 相关性分析结果

- **正相关**: 港股通持股比例增加，股价倾向于上涨
- **负相关**: 港股通持股比例减少，股价倾向于下跌
- **显著性**: P值<0.05表示相关性在统计上显著

### 策略回测结果

- **夏普比率>1**: 策略风险调整后收益较好
- **最大回撤<20%**: 策略风险控制较好
- **胜率>50%**: 策略有一定的预测能力

## ⚠️ 注意事项

1. **数据质量**: 确保港股通数据的完整性和准确性
2. **样本偏差**: 分析结果可能受到样本期间市场环境影响
3. **交易成本**: 回测未考虑交易成本和滑点
4. **前瞻偏差**: 避免使用未来信息进行历史分析

## 🛠️ 依赖包

```python
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.3.0
seaborn>=0.11.0
scipy>=1.7.0
scikit-learn>=0.24.0
```

## 📞 使用说明

1. 确保数据文件 `../data/h_shares_daily.parquet` 存在
2. 运行 `python run_hsgt_analysis.py` 开始分析
3. 查看各目录下的输出文件和图表
4. 根据分析结果调整策略参数并重新测试

## 🔄 更新日志

- **v1.0**: 初始版本，包含基础相关性分析和策略回测
- 支持多时间框架分析
- 支持可视化展示
- 支持策略绩效评估
